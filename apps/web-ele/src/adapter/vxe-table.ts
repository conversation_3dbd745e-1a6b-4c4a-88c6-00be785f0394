import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';
import type { Recordable } from '@vben/types';

import type { ComponentType } from './component';

import { h } from 'vue';

import { IconifyIcon } from '@vben/icons';
import { setupVbenVxeTable, useVbenVxeGrid as useGrid } from '@vben/plugins/vxe-table';
import { get, isFunction, isImageUrl, isString } from '@vben/utils';

import { objectOmit } from '@vueuse/core';
import {
  ElButton as Button,
  ElImage as Image,
  ElInputNumber as InputNumber,
  ElPopconfirm as Popconfirm,
  ElSwitch as Switch,
  ElTag as Tag,
} from 'element-plus';

import { useVbenForm } from './form';

setupVbenVxeTable({
  configVxeTable: (vxeUI) => {
    vxeUI.setConfig({
      grid: {
        align: 'center',
        border: true,
        columnConfig: {
          resizable: true,
        },
        height: 'auto',
        keepSource: true,
        pagerConfig: {
          enabled: true,
        },
        formConfig: {
          // 全局禁用vxe-table的表单配置，使用formOptions
          enabled: false,
        },
        sortConfig: {
          remote: true,
          trigger: 'cell',
        },
        minHeight: 180,
        proxyConfig: {
          sort: true,
          autoLoad: true,
          response: {
            result: 'items',
            total: 'total',
            list: '',
          },
          showActiveMsg: true,
          showResponseMsg: false,
        },
        toolbarConfig: {
          custom: true,
          export: false,
          refresh: { code: 'query' },
          zoom: true,
        },

        round: true,
        showOverflow: true,
        size: 'medium',
      } as VxeTableGridOptions,
    });

    /**
     * 解决vxeTable在热更新时可能会出错的问题
     */
    vxeUI.renderer.forEach((_item, key) => {
      if (key.startsWith('Cell')) {
        vxeUI.renderer.delete(key);
      }
    });

    // 表格配置项可以用 cellRender: { name: 'CellImage' },
    vxeUI.renderer.add('CellImage', {
      renderTableDefault(renderOpts, params) {
        const { attrs } = renderOpts;
        const { column, row } = params;
        const value = row[column.field];
        const isImage = isImageUrl(value);
        if (isImage) {
          return h(Image, {
            src: value,
            previewSrcList: [value],
            previewTeleported: true,
            ...attrs,
          });
        }
        return h(
          Button,
          {
            size: 'small',
            type: 'primary',
            link: true,
            onClick: () => {
              // 可以通过 attrs.onView 自定义查看逻辑
              if (attrs?.onView) {
                attrs.onView(value, row, column);
              } else {
                // 默认行为：在新窗口打开链接
                if (value && typeof value === 'string') {
                  window.open(value, '_blank');
                }
              }
            },
            ...attrs,
          },
          { default: () => '查看' },
        );
      },
    });

    // 表格配置项可以用 cellRender: { name: 'CellLink' },
    vxeUI.renderer.add('CellLink', {
      renderTableDefault(renderOpts) {
        const { props } = renderOpts;
        return h(Button, { size: 'small', type: 'primary', link: true }, { default: () => props?.text });
      },
    });

    // 单元格渲染： Tag
    vxeUI.renderer.add('CellTag', {
      renderTableDefault({ options, props }, { column, row }) {
        const value = get(row, column.field);
        const tagOptions = options ?? [
          { type: 'primary', label: '启用', value: 1 },
          { type: 'danger', label: '禁用', value: 2 },
        ];
        const tagItem = tagOptions.find((item) => item.value === value);
        return h(
          Tag,
          {
            ...props,
            ...objectOmit(tagItem ?? {}, ['label']),
          },
          { default: () => tagItem?.label ?? value },
        );
      },
    });

    vxeUI.renderer.add('CellSwitch', {
      renderTableDefault({ attrs, props }, { column, row }) {
        const finallyProps = {
          activeValue: 1,
          inactiveValue: 2,
          ...props,
          modelValue: row[column.field],
          'onUpdate:modelValue': onChange,
        };
        async function onChange(newVal: any) {
          try {
            const result = await attrs?.beforeChange?.(newVal, row);
            if (result !== false) {
              row[column.field] = newVal;
            }
          } catch (error) {
            console.error(error);
          }
        }
        return h(Switch, finallyProps);
      },
    });

    // 数字输入框适配器：CellInputNumber
    vxeUI.renderer.add('CellInputNumber', {
      renderTableDefault({ attrs, props }, { column, row }) {
        const finallyProps = {
          ...props,
          modelValue: row[column.field],
          'onUpdate:modelValue': (newVal: any) => {
            row[column.field] = newVal;
          },
          onChange,
        };
        async function onChange(newVal: any) {
          try {
            const result = await attrs?.beforeChange?.(newVal, row);
            if (result !== false) {
              row[column.field] = newVal;
            }
          } catch (error) {
            console.error(error);
          }
        }
        return h(InputNumber, finallyProps);
      },
    });

    /**
     * 注册表格的操作按钮渲染器
     */
    vxeUI.renderer.add('CellOperation', {
      renderTableDefault({ attrs, options, props }, { column, row }) {
        const defaultProps = {
          size: 'small',
          type: 'primary',
          link: true,
          ...props,
        };
        let align = 'end';
        switch (column.align) {
          case 'center': {
            align = 'center';
            break;
          }
          case 'left': {
            align = 'start';
            break;
          }
          default: {
            align = 'end';
            break;
          }
        }
        const presets: Recordable<Recordable<any>> = {
          delete: {
            icon: 'mdi-trash-can-outline',
            buttonText: '删除',
          },
          edit: {
            icon: 'mdi-playlist-edit',
            buttonText: '编辑',
          },
        };
        const operations: Array<Recordable<any>> = (options || ['edit', 'delete'])
          .map((opt) => {
            if (isString(opt)) {
              return presets[opt]
                ? { code: opt, ...presets[opt], ...defaultProps }
                : {
                    code: opt,
                    ...defaultProps,
                  };
            } else {
              return { ...defaultProps, ...presets[opt.code], ...opt };
            }
          })
          .map((opt) => {
            const optBtn: Recordable<any> = {};
            Object.keys(opt).forEach((key) => {
              optBtn[key] = isFunction(opt[key]) ? opt[key](row) : opt[key];
            });
            return optBtn;
          })
          .filter((opt) => opt.show !== false);

        function renderBtn(opt: Recordable<any>, listen = true) {
          return h(
            Button,
            {
              ...props,
              ...opt,
              icon: undefined,
              onClick: listen
                ? () =>
                    attrs?.onClick?.({
                      code: opt.code,
                      row,
                    })
                : undefined,
            },
            {
              default: () => {
                const content = [];
                if (opt.icon) {
                  content.push(h(IconifyIcon, { class: 'size-5 mr-1', icon: opt.icon }));
                }
                content.push(opt.buttonText);
                return content;
              },
            },
          );
        }

        function renderConfirm(opt: Recordable<any>) {
          return h(
            Popconfirm,
            {
              placement: 'top-start',
              title: `确认删除${attrs?.nameTitle || ''}？`,
              hideAfter: 0,
              ...props,
              ...opt,
              icon: undefined,
              onConfirm: () => {
                attrs?.onClick?.({
                  code: opt.code,
                  row,
                });
              },
              onCancel: () => {
                console.warn('cancel');
              },
            },
            {
              reference: () => renderBtn({ ...opt }, false),
            },
          );
        }

        const btns = operations.map((opt) =>
          opt.code === 'delete' ? renderConfirm({ ...opt, type: 'danger' }) : renderBtn(opt),
        );
        return h(
          'div',
          {
            class: 'flex table-operations',
            style: { justifyContent: align },
          },
          btns,
        );
      },
    });

    // 这里可以自行扩展 vxe-table 的全局配置，比如自定义格式化
    // vxeUI.formats.add
  },
  useVbenForm,
});

export function vxeCheckboxChecked(tableApi: ReturnType<typeof useVbenVxeGrid>[1]) {
  return tableApi?.grid?.getCheckboxRecords?.()?.length > 0;
}

export const useVbenVxeGrid = <T extends Record<string, any>>(...rest: Parameters<typeof useGrid<T, ComponentType>>) =>
  useGrid<T, ComponentType>(...rest);

export type OnActionClickParams<T = Recordable<any>> = {
  code: string;
  row: T;
};
export type OnActionClickFn<T = Recordable<any>> = (params: OnActionClickParams<T>) => void;
export type * from '@vben/plugins/vxe-table';
