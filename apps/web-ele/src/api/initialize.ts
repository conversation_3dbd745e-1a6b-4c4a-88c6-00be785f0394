import type { ApiClient } from '@vben/api';

import { setApiClient, setBaseApiClient } from '@vben/api';

import { baseRequestClient, requestClient } from './request';

/**
 * 初始化 API 客户端
 * 将应用特有的客户端适配到共享接口并注册
 */
export function initializeApi(): void {
  // 将应用特有的客户端适配到共享接口
  const apiClient: ApiClient = {
    get: requestClient.get.bind(requestClient),
    post: requestClient.post.bind(requestClient),
    put: requestClient.put.bind(requestClient),
    delete: requestClient.delete.bind(requestClient),
    request: requestClient.request.bind(requestClient),
    upload: requestClient.upload.bind(requestClient),
  };

  const baseApiClient: ApiClient = {
    get: baseRequestClient.get.bind(baseRequestClient),
    post: baseRequestClient.post.bind(baseRequestClient),
    put: baseRequestClient.put.bind(baseRequestClient),
    delete: baseRequestClient.delete.bind(baseRequestClient),
    request: baseRequestClient.request.bind(baseRequestClient),
    upload: baseRequestClient.upload.bind(baseRequestClient),
  };

  // 注册应用的客户端实例
  setApiClient(apiClient);
  setBaseApiClient(baseApiClient);

  // 如需调试可使用以下代码
  // console.warn('API 客户端初始化完成');
}

// 注意：此处不导出 request 和 core，避免循环引用
// 如需使用相关模块，请从 index.ts 导入
