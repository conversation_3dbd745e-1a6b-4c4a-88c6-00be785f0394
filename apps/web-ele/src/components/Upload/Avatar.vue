<script setup lang="ts" name="UploadAvatar">
import type { UploadRequestOptions } from 'element-plus';

import type { AvatarUploadProps } from './types';

import { computed, inject, ref } from 'vue';

import { uploadImageApi } from '@vben/api';

import { ElIcon, ElImageViewer, ElUpload, formContextKey, formItemContextKey } from 'element-plus';

import { useCosUpload } from './hooks/useCosUpload';
import { useUpload } from './hooks/useUpload';

// 接受父组件参数
const props = withDefaults(defineProps<AvatarUploadProps>(), {
  avatar: '',
  size: '100px',
  circle: true,
  disabled: false,
  fileSize: 2,
  fileType: () => ['image/jpeg', 'image/png'],
  useCos: false,
});

const emit = defineEmits<{
  'update:avatar': [value: string];
}>();

// 获取表单上下文
const formContext = inject(formContextKey, void 0);
const formItemContext = inject(formItemContextKey, void 0);

// 判断是否禁用
const isDisabled = computed(() => {
  return props.disabled || formContext?.disabled;
});

// 图片预览
const previewVisible = ref(false);

// 使用上传hooks
const { beforeUpload, generateId } = useUpload({
  fileSize: props.fileSize,
  fileType: props.fileType,
});

const { cosUpload } = useCosUpload();

/**
 * 处理文件上传
 */
const handleUpload = async (options: UploadRequestOptions) => {
  try {
    let avatarUrl: string;

    if (props.useCos) {
      // 使用COS直传
      avatarUrl = await cosUpload(options.file, props.cosParams);
    } else {
      // 使用普通上传
      const api = props.api ?? uploadImageApi;
      avatarUrl = await api({ file: options.file });
    }

    // 更新头像
    emit('update:avatar', avatarUrl);

    // 触发表单验证
    if (formItemContext?.prop && formContext?.validateField) {
      formContext.validateField([formItemContext.prop as string]);
    }
  } catch (error) {
    options.onError(error as any);
  }
};

/**
 * 删除头像
 */
const removeAvatar = () => {
  emit('update:avatar', '');
};

/**
 * 预览头像
 */
const previewAvatar = () => {
  if (props.avatar) {
    previewVisible.value = true;
  }
};

/**
 * 编辑头像
 */
const editAvatar = () => {
  const dom = document.querySelector(`#${uploadId} .el-upload__input`);
  dom && dom.dispatchEvent(new MouseEvent('click'));
};

// 生成唯一ID
const uploadId = generateId();
</script>

<template>
  <div class="upload-avatar">
    <ElUpload
      :id="uploadId"
      action="#"
      class="avatar-uploader"
      :class="[{ disabled: isDisabled }, { circle }]"
      :multiple="false"
      :disabled="isDisabled"
      :show-file-list="false"
      :http-request="handleUpload"
      :before-upload="beforeUpload"
      :accept="fileType.join(',')"
    >
      <div class="avatar-container">
        <!-- 已有头像 -->
        <template v-if="avatar">
          <img :src="avatar" class="avatar-image" />
          <div class="avatar-mask" @click.stop>
            <div class="mask-actions">
              <div class="action-btn" @click="previewAvatar">
                <ElIcon><ZoomIn /></ElIcon>
              </div>
              <div v-if="!isDisabled" class="action-btn" @click="editAvatar">
                <ElIcon><Edit /></ElIcon>
              </div>
              <div v-if="!isDisabled" class="action-btn" @click="removeAvatar">
                <ElIcon><Delete /></ElIcon>
              </div>
            </div>
          </div>
        </template>

        <!-- 空状态 -->
        <template v-else>
          <div class="avatar-placeholder">
            <ElIcon class="placeholder-icon"><Plus /></ElIcon>
            <div class="placeholder-text">上传头像</div>
          </div>
        </template>
      </div>
    </ElUpload>

    <!-- 提示信息 -->
    <div class="upload-tip">
      <slot name="tip">
        <span>建议尺寸：1:1，文件大小不超过 {{ fileSize }}MB</span>
      </slot>
    </div>

    <!-- 头像预览 -->
    <ElImageViewer v-if="previewVisible && avatar" :url-list="[avatar]" @close="previewVisible = false" teleported />
  </div>
</template>

<style scoped lang="scss">
.upload-avatar {
  display: inline-block;

  .avatar-uploader {
    :deep(.el-upload) {
      position: relative;
      width: v-bind(size);
      height: v-bind(size);
      overflow: hidden;
      cursor: pointer;
      border: 1px dashed var(--el-border-color-darker);
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        border-color: var(--el-color-primary);

        .avatar-mask {
          opacity: 1;
        }
      }

      &.disabled {
        cursor: not-allowed;
        background: var(--el-disabled-bg-color);
        border-color: var(--el-border-color-darker);

        &:hover {
          border-color: var(--el-border-color-darker);
        }
      }
    }

    &.circle {
      :deep(.el-upload) {
        border-radius: 50%;
      }

      .avatar-image,
      .avatar-placeholder {
        border-radius: 50%;
      }
    }

    .avatar-container {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;

      .avatar-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .avatar-mask {
        position: absolute;
        inset: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgb(0 0 0 / 60%);
        opacity: 0;
        transition: opacity 0.3s ease;

        .mask-actions {
          display: flex;
          gap: 8px;

          .action-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            color: white;
            cursor: pointer;
            background: rgb(255 255 255 / 20%);
            border-radius: 4px;
            transition: all 0.2s ease;

            &:hover {
              background: rgb(255 255 255 / 30%);
              transform: scale(1.1);
            }

            .el-icon {
              font-size: 16px;
            }
          }
        }
      }

      .avatar-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        color: var(--el-text-color-secondary);
        background: var(--el-bg-color-page);

        .placeholder-icon {
          margin-bottom: 8px;
          font-size: 32px;
        }

        .placeholder-text {
          font-size: 12px;
        }
      }
    }
  }

  .upload-tip {
    margin-top: 8px;
    font-size: 12px;
    line-height: 1.4;
    color: var(--el-text-color-regular);
    text-align: center;
  }
}

:deep(.disabled) {
  .el-upload {
    cursor: not-allowed !important;
    background: var(--el-disabled-bg-color);
    border-color: var(--el-border-color-darker) !important;

    &:hover {
      border-color: var(--el-border-color-darker) !important;
    }
  }
}
</style>
