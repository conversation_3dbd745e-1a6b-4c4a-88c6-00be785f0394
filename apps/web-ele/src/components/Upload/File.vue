<script setup lang="ts" name="UploadFile">
import type { UploadRequestOptions } from 'element-plus';

import type { FileUploadProps } from './types';

import { computed, inject, ref } from 'vue';

import { uploadImageApi } from '@vben/api';

import { ElButton, ElIcon, ElProgress, ElUpload, formContextKey, formItemContextKey } from 'element-plus';

import { useCosUpload } from './hooks/useCosUpload';
import { useUpload } from './hooks/useUpload';

// 接受父组件参数
const props = withDefaults(defineProps<FileUploadProps>(), {
  fileUrl: '',
  fileName: '',
  accept: '',
  drag: true,
  disabled: false,
  fileSize: 10,
  useCos: false,
});

const emit = defineEmits<{
  'update:fileName': [value: string];
  'update:fileUrl': [value: string];
}>();

// 获取表单上下文
const formContext = inject(formContext<PERSON>ey, void 0);
const formItemContext = inject(formItemContextKey, void 0);

// 判断是否禁用
const isDisabled = computed(() => {
  return props.disabled || formContext?.disabled;
});

// 上传状态
const uploading = ref(false);
const uploadProgress = ref(0);

// 使用上传hooks
const { beforeUpload: baseBeforeUpload, generateId } = useUpload({
  fileSize: props.fileSize,
  accept: props.accept,
});

const { cosUpload } = useCosUpload();

/**
 * 文件上传前验证
 */
const beforeUpload = (rawFile: File): boolean => {
  // 使用 useUpload hook 的验证逻辑
  return baseBeforeUpload(rawFile);
};

/**
 * 处理文件上传
 */
const handleUpload = async (options: UploadRequestOptions) => {
  try {
    uploading.value = true;
    uploadProgress.value = 0;

    let fileUrl: string;

    if (props.useCos) {
      // 使用COS直传
      fileUrl = await cosUpload(options.file, props.cosParams, (progress) => {
        uploadProgress.value = progress;
      });
    } else {
      // 使用普通上传
      const api = props.api ?? uploadImageApi;

      // 模拟进度
      const progressTimer = setInterval(() => {
        if (uploadProgress.value < 90) {
          uploadProgress.value += 10;
        }
      }, 100);

      fileUrl = await api({ file: options.file });

      clearInterval(progressTimer);
      uploadProgress.value = 100;
    }

    // 更新文件信息
    emit('update:fileUrl', fileUrl);
    emit('update:fileName', options.file.name);

    // 触发表单验证
    if (formItemContext?.prop && formContext?.validateField) {
      formContext.validateField([formItemContext.prop as string]);
    }
  } catch (error) {
    options.onError(error as any);
  } finally {
    uploading.value = false;
    uploadProgress.value = 0;
  }
};

/**
 * 删除文件
 */
const removeFile = () => {
  emit('update:fileUrl', '');
  emit('update:fileName', '');
};

/**
 * 下载文件
 */
const downloadFile = () => {
  if (props.fileUrl) {
    const link = document.createElement('a');
    link.href = props.fileUrl;
    link.download = props.fileName || '下载文件';
    link.target = '_blank';
    document.body.append(link);
    link.click();
    link.remove();
  }
};

/**
 * 获取文件图标
 */
const getFileIcon = (fileName: string): string => {
  const ext = fileName.split('.').pop()?.toLowerCase() || '';
  const iconMap: Record<string, string> = {
    pdf: 'Document',
    doc: 'Document',
    docx: 'Document',
    xls: 'Document',
    xlsx: 'Document',
    ppt: 'Document',
    pptx: 'Document',
    txt: 'Document',
    zip: 'FolderOpened',
    rar: 'FolderOpened',
    '7z': 'FolderOpened',
    jpg: 'Picture',
    jpeg: 'Picture',
    png: 'Picture',
    gif: 'Picture',
    mp4: 'VideoPlay',
    avi: 'VideoPlay',
    mov: 'VideoPlay',
    mp3: 'Headphone',
    wav: 'Headphone',
  };
  return iconMap[ext] || 'Document';
};

// 生成唯一ID
const uploadId = generateId();
</script>

<template>
  <div class="upload-file">
    <!-- 已上传文件显示 -->
    <div v-if="fileUrl" class="file-item">
      <div class="file-info">
        <ElIcon class="file-icon">
          <component :is="getFileIcon(fileName)" />
        </ElIcon>
        <div class="file-details">
          <div class="file-name" :title="fileName">{{ fileName }}</div>
          <div class="file-actions">
            <ElButton type="primary" link size="small" @click="downloadFile">
              <ElIcon><Download /></ElIcon>
              下载
            </ElButton>
            <ElButton v-if="!isDisabled" type="danger" link size="small" @click="removeFile">
              <ElIcon><Delete /></ElIcon>
              删除
            </ElButton>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传区域 -->
    <div v-else class="upload-area">
      <ElUpload
        :id="uploadId"
        action="#"
        class="uploader"
        :class="{ disabled: isDisabled }"
        :multiple="false"
        :disabled="isDisabled"
        :show-file-list="false"
        :http-request="handleUpload"
        :before-upload="beforeUpload"
        :drag="drag"
        :accept="accept"
      >
        <div v-if="drag" class="upload-dragger">
          <ElIcon class="upload-icon"><UploadFilled /></ElIcon>
          <div class="upload-text">
            <div>点击或拖拽文件到此区域上传</div>
            <div class="upload-hint">
              <slot name="tip"> 支持单个文件上传，文件大小不超过 {{ fileSize }}MB </slot>
            </div>
          </div>
        </div>
        <ElButton v-else type="primary" :disabled="isDisabled">
          <ElIcon><Plus /></ElIcon>
          选择文件
        </ElButton>
      </ElUpload>
    </div>

    <!-- 上传进度 -->
    <div v-if="uploading" class="upload-progress">
      <ElProgress :percentage="uploadProgress" :show-text="true" />
      <div class="progress-text">正在上传...</div>
    </div>

    <!-- 提示信息 -->
    <div v-if="!fileUrl && !drag" class="upload-tip">
      <slot name="tip"> 支持的文件格式：{{ accept || '所有格式' }}，文件大小不超过 {{ fileSize }}MB </slot>
    </div>
  </div>
</template>

<style scoped lang="scss">
.upload-file {
  .file-item {
    padding: 12px;
    background: var(--el-bg-color-page);
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 8px;

    .file-info {
      display: flex;
      gap: 12px;
      align-items: center;

      .file-icon {
        flex-shrink: 0;
        font-size: 32px;
        color: var(--el-color-primary);
      }

      .file-details {
        flex: 1;
        min-width: 0;

        .file-name {
          margin-bottom: 8px;
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: 14px;
          font-weight: 500;
          color: var(--el-text-color-primary);
          white-space: nowrap;
        }

        .file-actions {
          display: flex;
          gap: 8px;
        }
      }
    }
  }

  .upload-area {
    .uploader {
      width: 100%;

      :deep(.el-upload) {
        width: 100%;

        &.disabled {
          cursor: not-allowed;
          opacity: 0.6;
        }
      }

      :deep(.el-upload-dragger) {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 120px;
        background: var(--el-bg-color-page);
        border: 1px dashed var(--el-border-color-darker);
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
          background: var(--el-color-primary-light-9);
          border-color: var(--el-color-primary);
        }

        &.is-dragover {
          background: var(--el-color-primary-light-8);
          border-color: var(--el-color-primary);
        }
      }
    }

    .upload-dragger {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;

      .upload-icon {
        margin-bottom: 16px;
        font-size: 48px;
        color: var(--el-color-primary);
      }

      .upload-text {
        color: var(--el-text-color-regular);

        .upload-hint {
          margin-top: 8px;
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
      }
    }
  }

  .upload-progress {
    margin-top: 16px;

    .progress-text {
      margin-top: 8px;
      font-size: 12px;
      color: var(--el-text-color-secondary);
      text-align: center;
    }
  }

  .upload-tip {
    margin-top: 8px;
    font-size: 12px;
    line-height: 1.4;
    color: var(--el-text-color-regular);
  }
}

:deep(.disabled) {
  .el-upload,
  .el-upload-dragger {
    cursor: not-allowed !important;
    background: var(--el-disabled-bg-color) !important;
    border-color: var(--el-border-color-darker) !important;

    &:hover {
      background: var(--el-disabled-bg-color) !important;
      border-color: var(--el-border-color-darker) !important;
    }
  }
}
</style>
