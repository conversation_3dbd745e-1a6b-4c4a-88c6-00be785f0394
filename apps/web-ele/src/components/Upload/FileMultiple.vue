<script setup lang="ts" name="UploadFileMultiple">
import type { UploadRequestOptions } from 'element-plus';

import type { MultipleFileUploadProps, UploadFile } from './types';

import { computed, inject, ref } from 'vue';

import { uploadImageApi } from '@vben/api';

import { ElButton, ElIcon, ElNotification, ElProgress, ElUpload, formContextKey, formItemContextKey } from 'element-plus';

import { useCosUpload } from './hooks/useCosUpload';
import { useUpload } from './hooks/useUpload';

// 接受父组件参数
const props = withDefaults(defineProps<MultipleFileUploadProps>(), {
  modelValue: () => [],
  maxCount: 10,
  accept: '',
  drag: true,
  disabled: false,
  fileSize: 10,
  useCos: false,
});

const emit = defineEmits<{
  'update:modelValue': [value: UploadFile[]];
}>();

// 获取表单上下文
const formContext = inject(formContextKey, void 0);
const formItemContext = inject(formItemContextKey, void 0);

// 判断是否禁用
const isDisabled = computed(() => {
  return props.disabled || formContext?.disabled;
});

// 是否达到最大上传数量
const isMaxCount = computed(() => {
  return props.modelValue.length >= props.maxCount;
});

// 上传状态
const uploadingFiles = ref<Map<string, { progress: number; status: 'error' | 'success' | 'uploading' }>>(new Map());

// 使用上传hooks
const { beforeUpload: baseBeforeUpload, generateId } = useUpload({
  fileSize: props.fileSize,
  accept: props.accept,
});

const { cosUpload } = useCosUpload();

/**
 * 文件上传前验证
 */
const beforeUpload = (rawFile: File): boolean => {
  // 只进行基础验证（文件大小、类型等），数量限制在 handleUpload 中处理
  return baseBeforeUpload(rawFile);
};

/**
 * 处理文件上传
 */
const handleUpload = async (options: UploadRequestOptions) => {
  // 检查文件数量限制（包括正在上传的文件）
  const totalCount = props.modelValue.length + uploadingFiles.value.size;
  if (totalCount >= props.maxCount) {
    ElNotification({
      title: '温馨提示',
      message: `最多只能上传 ${props.maxCount} 个文件！`,
      type: 'warning',
    });
    options.onError(new Error('文件数量超出限制') as any);
    return;
  }

  const fileId = generateId();

  try {
    // 添加到上传状态
    uploadingFiles.value.set(fileId, { progress: 0, status: 'uploading' });

    let fileUrl: string;

    if (props.useCos) {
      // 使用COS直传
      fileUrl = await cosUpload(options.file, props.cosParams, (progress) => {
        const uploadInfo = uploadingFiles.value.get(fileId);
        if (uploadInfo) {
          uploadInfo.progress = progress;
        }
      });
    } else {
      // 使用普通上传
      const api = props.api ?? uploadImageApi;

      // 模拟进度
      const progressTimer = setInterval(() => {
        const uploadInfo = uploadingFiles.value.get(fileId);
        if (uploadInfo && uploadInfo.progress < 90) {
          uploadInfo.progress += 10;
        }
      }, 100);

      fileUrl = await api({ file: options.file });

      clearInterval(progressTimer);
      const uploadInfo = uploadingFiles.value.get(fileId);
      if (uploadInfo) {
        uploadInfo.progress = 100;
      }
    }

    // 创建文件对象
    const newFile: UploadFile = {
      uid: fileId,
      name: options.file.name,
      url: fileUrl,
      size: options.file.size,
      type: options.file.type,
      status: 'success',
      progress: 100,
    };

    // 更新文件列表
    const newFiles = [...props.modelValue, newFile];
    emit('update:modelValue', newFiles);

    // 更新上传状态
    uploadingFiles.value.set(fileId, { progress: 100, status: 'success' });

    // 触发表单验证
    if (formItemContext?.prop && formContext?.validateField) {
      formContext.validateField([formItemContext.prop as string]);
    }

    // 延迟移除上传状态
    setTimeout(() => {
      uploadingFiles.value.delete(fileId);
    }, 1000);
  } catch (error) {
    // 更新错误状态
    uploadingFiles.value.set(fileId, { progress: 0, status: 'error' });

    setTimeout(() => {
      uploadingFiles.value.delete(fileId);
    }, 3000);

    options.onError(error as any);
  }
};

/**
 * 删除文件
 */
const removeFile = (index: number) => {
  const newFiles = props.modelValue.filter((_, i) => i !== index);
  emit('update:modelValue', newFiles);
};

/**
 * 下载文件
 */
const downloadFile = (file: UploadFile) => {
  if (file.url) {
    const link = document.createElement('a');
    link.href = file.url;
    link.download = file.name;
    link.target = '_blank';
    document.body.append(link);
    link.click();
    link.remove();
  }
};

/**
 * 获取文件图标
 */
const getFileIcon = (fileName: string): string => {
  const ext = fileName.split('.').pop()?.toLowerCase() || '';
  const iconMap: Record<string, string> = {
    pdf: 'Document',
    doc: 'Document',
    docx: 'Document',
    xls: 'Document',
    xlsx: 'Document',
    ppt: 'Document',
    pptx: 'Document',
    txt: 'Document',
    zip: 'FolderOpened',
    rar: 'FolderOpened',
    '7z': 'FolderOpened',
    jpg: 'Picture',
    jpeg: 'Picture',
    png: 'Picture',
    gif: 'Picture',
    mp4: 'VideoPlay',
    avi: 'VideoPlay',
    mov: 'VideoPlay',
    mp3: 'Headphone',
    wav: 'Headphone',
  };
  return iconMap[ext] || 'Document';
};

/**
 * 格式化文件大小
 */
const formatFileSize = (size: number): string => {
  if (size < 1024) return `${size}B`;
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`;
  return `${(size / 1024 / 1024).toFixed(1)}MB`;
};

// 生成唯一ID
const uploadId = generateId();
</script>

<template>
  <div class="upload-file-multiple">
    <!-- 文件列表 -->
    <div v-if="modelValue.length > 0" class="file-list">
      <div v-for="(file, index) in modelValue" :key="file.uid || index" class="file-item">
        <div class="file-info">
          <ElIcon class="file-icon">
            <component :is="getFileIcon(file.name)" />
          </ElIcon>
          <div class="file-details">
            <div class="file-name" :title="file.name">{{ file.name }}</div>
            <div class="file-meta">
              <span v-if="file.size > 0 && file.size > 0" class="file-size">{{ formatFileSize(file.size || 0) }}</span>
              <span class="file-status" :class="file.status">
                {{ file.status === 'success' ? '上传成功' : file.status === 'error' ? '上传失败' : '上传中' }}
              </span>
            </div>
          </div>
          <div class="file-actions">
            <ElButton type="primary" link size="small" @click="downloadFile(file)">
              <ElIcon><Download /></ElIcon>
              下载
            </ElButton>
            <ElButton v-if="!isDisabled" type="danger" link size="small" @click="removeFile(index)">
              <ElIcon><Delete /></ElIcon>
              删除
            </ElButton>
          </div>
        </div>

        <!-- 文件进度条 -->
        <div v-if="file.status === 'uploading' && file.progress !== undefined" class="file-progress">
          <ElProgress :percentage="file.progress" :show-text="false" />
        </div>
      </div>
    </div>

    <!-- 上传中的文件 -->
    <div v-if="uploadingFiles.size > 0" class="uploading-list">
      <div v-for="[fileId, uploadInfo] in uploadingFiles" :key="fileId" class="uploading-item">
        <div class="uploading-info">
          <ElIcon class="upload-icon"><UploadFilled /></ElIcon>
          <span class="upload-text">正在上传...</span>
          <span class="upload-status" :class="uploadInfo.status">
            {{
              uploadInfo.status === 'uploading'
                ? `${uploadInfo.progress}%`
                : uploadInfo.status === 'error'
                  ? '上传失败'
                  : '上传成功'
            }}
          </span>
        </div>
        <div v-if="uploadInfo.status === 'uploading'" class="upload-progress">
          <ElProgress :percentage="uploadInfo.progress" :show-text="false" />
        </div>
      </div>
    </div>

    <!-- 上传区域 -->
    <div class="upload-area">
      <ElUpload
        :id="uploadId"
        action="#"
        class="uploader"
        :class="{ disabled: isDisabled || isMaxCount }"
        :multiple="true"
        :disabled="isDisabled || isMaxCount"
        :show-file-list="false"
        :http-request="handleUpload"
        :before-upload="beforeUpload"
        :drag="drag"
        :accept="accept"
      >
        <div v-if="drag" class="upload-dragger">
          <ElIcon class="upload-icon"><UploadFilled /></ElIcon>
          <div class="upload-text">
            <div>点击或拖拽文件到此区域上传</div>
            <div class="upload-hint">
              <slot name="tip"> 支持多个文件上传，最多 {{ maxCount }} 个文件，单个文件不超过 {{ fileSize }}MB </slot>
            </div>
          </div>
        </div>
        <ElButton v-else type="primary" :disabled="isDisabled || isMaxCount">
          <ElIcon><Plus /></ElIcon>
          选择文件
        </ElButton>
      </ElUpload>
    </div>

    <!-- 提示信息 -->
    <div v-if="!drag" class="upload-tip">
      <slot name="tip">
        支持的文件格式：{{ accept || '所有格式' }}，最多 {{ maxCount }} 个文件，单个文件不超过 {{ fileSize }}MB
      </slot>
    </div>
  </div>
</template>

<style scoped lang="scss">
.upload-file-multiple {
  .file-list {
    margin-bottom: 16px;

    .file-item {
      padding: 12px;
      margin-bottom: 8px;
      background: var(--el-bg-color-page);
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        border-color: var(--el-color-primary-light-5);
        box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
      }

      &:last-child {
        margin-bottom: 0;
      }

      .file-info {
        display: flex;
        gap: 12px;
        align-items: center;

        .file-icon {
          flex-shrink: 0;
          font-size: 32px;
          color: var(--el-color-primary);
        }

        .file-details {
          flex: 1;
          min-width: 0;

          .file-name {
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 14px;
            font-weight: 500;
            line-height: 1.4;
            color: var(--el-text-color-primary);
            white-space: nowrap;
          }

          .file-meta {
            display: flex;
            gap: 12px;
            align-items: center;
            font-size: 12px;
            color: var(--el-text-color-secondary);

            .file-size {
              color: var(--el-text-color-regular);
            }

            .file-status {
              &.success {
                color: var(--el-color-success);
              }

              &.error {
                color: var(--el-color-error);
              }

              &.uploading {
                color: var(--el-color-warning);
              }
            }
          }
        }

        .file-actions {
          display: flex;
          flex-shrink: 0;
          gap: 8px;
        }
      }

      .file-progress {
        margin-top: 8px;
      }
    }
  }

  .uploading-list {
    margin-bottom: 16px;

    .uploading-item {
      padding: 12px;
      margin-bottom: 8px;
      background: var(--el-color-primary-light-9);
      border: 1px solid var(--el-color-primary-light-7);
      border-radius: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .uploading-info {
        display: flex;
        gap: 8px;
        align-items: center;
        font-size: 14px;

        .upload-icon {
          color: var(--el-color-primary);
        }

        .upload-text {
          flex: 1;
          color: var(--el-text-color-primary);
        }

        .upload-status {
          font-size: 12px;

          &.uploading {
            color: var(--el-color-warning);
          }

          &.success {
            color: var(--el-color-success);
          }

          &.error {
            color: var(--el-color-error);
          }
        }
      }

      .upload-progress {
        margin-top: 8px;
      }
    }
  }

  .upload-area {
    .uploader {
      width: 100%;

      :deep(.el-upload) {
        width: 100%;

        &.disabled {
          cursor: not-allowed;
          opacity: 0.6;
        }
      }

      :deep(.el-upload-dragger) {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 120px;
        background: var(--el-bg-color-page);
        border: 1px dashed var(--el-border-color-darker);
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
          background: var(--el-color-primary-light-9);
          border-color: var(--el-color-primary);
        }

        &.is-dragover {
          background: var(--el-color-primary-light-8);
          border-color: var(--el-color-primary);
        }
      }
    }

    .upload-dragger {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;

      .upload-icon {
        margin-bottom: 16px;
        font-size: 48px;
        color: var(--el-color-primary);
      }

      .upload-text {
        color: var(--el-text-color-regular);

        .upload-hint {
          margin-top: 8px;
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
      }
    }
  }

  .upload-tip {
    margin-top: 8px;
    font-size: 12px;
    line-height: 1.4;
    color: var(--el-text-color-regular);
  }
}

:deep(.disabled) {
  .el-upload,
  .el-upload-dragger {
    cursor: not-allowed !important;
    background: var(--el-disabled-bg-color) !important;
    border-color: var(--el-border-color-darker) !important;

    &:hover {
      background: var(--el-disabled-bg-color) !important;
      border-color: var(--el-border-color-darker) !important;
    }
  }
}
</style>
