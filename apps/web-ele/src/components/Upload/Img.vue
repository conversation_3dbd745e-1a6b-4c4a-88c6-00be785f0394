<script setup lang="ts" name="UploadImg">
import type { UploadRequestOptions } from 'element-plus';

import type { ImageUploadProps } from './types';

import { computed, inject, ref } from 'vue';

import { uploadImageApi } from '@vben/api';

import { ElIcon, ElImageViewer, ElUpload, formContextKey, formItemContextKey } from 'element-plus';

import { useCosUpload } from './hooks/useCosUpload';
import { useUpload } from './hooks/useUpload';

// 接受父组件参数
const props = withDefaults(defineProps<ImageUploadProps>(), {
  imageUrl: '',
  api: undefined,
  drag: true,
  disabled: false,
  fileSize: 5,
  fileType: () => ['image/jpeg', 'image/png', 'image/gif'],
  height: '150px',
  width: '150px',
  borderRadius: '8px',
  useCos: false,
});

const emit = defineEmits<{
  'update:imageUrl': [value: string];
}>();

// 获取表单上下文
const formContext = inject(formContextKey, void 0);
const formItemContext = inject(formItemContextKey, void 0);

// 判断是否禁用
const self_disabled = computed(() => {
  return props.disabled || formContext?.disabled;
});

// 图片预览
const imgViewVisible = ref(false);

// 使用上传hooks
const { beforeUpload, generateId } = useUpload({
  fileSize: props.fileSize,
  fileType: props.fileType,
});

const { cosUpload } = useCosUpload();

/**
 * 处理文件上传
 */
const handleHttpUpload = async (options: UploadRequestOptions) => {
  try {
    let imageUrl: string;

    if (props.useCos) {
      // 使用COS直传
      imageUrl = await cosUpload(options.file, props.cosParams);
    } else {
      // 使用普通上传
      const api = props.api ?? uploadImageApi;
      imageUrl = await api({ file: options.file });
    }

    emit('update:imageUrl', imageUrl);

    // 触发表单验证
    if (formItemContext?.prop && formContext?.validateField) {
      formContext.validateField([formItemContext.prop as string]);
    }
  } catch (error) {
    options.onError(error as any);
  }
};

/**
 * 删除图片
 */
const deleteImg = () => {
  emit('update:imageUrl', '');
};

/**
 * 编辑图片
 */
const editImg = () => {
  const dom = document.querySelector(`#${uuid} .el-upload__input`);
  dom && dom.dispatchEvent(new MouseEvent('click'));
};

// 生成唯一ID
const uuid = generateId();
</script>

<template>
  <div class="upload-box">
    <ElUpload
      :id="uuid"
      action="#"
      class="upload"
      :class="[self_disabled ? 'disabled' : '', drag ? 'no-border' : '']"
      :multiple="false"
      :disabled="self_disabled"
      :show-file-list="false"
      :http-request="handleHttpUpload"
      :before-upload="beforeUpload"
      :drag="drag"
      :accept="fileType.join(',')"
    >
      <template v-if="imageUrl">
        <img :src="imageUrl" class="upload-image" />
        <div class="upload-handle" @click.stop>
          <div v-if="!self_disabled" class="handle-icon" @click="editImg">
            <ElIcon><Edit /></ElIcon>
            <span>编辑</span>
          </div>
          <div class="handle-icon" @click="imgViewVisible = true">
            <ElIcon><ZoomIn /></ElIcon>
            <span>查看</span>
          </div>
          <div v-if="!self_disabled" class="handle-icon" @click="deleteImg">
            <ElIcon><Delete /></ElIcon>
            <span>删除</span>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="upload-empty">
          <slot name="empty">
            <ElIcon><Plus /></ElIcon>
            <span>请上传图片</span>
          </slot>
        </div>
      </template>
    </ElUpload>
    <div class="el-upload__tip">
      <slot name="tip"></slot>
    </div>
    <ElImageViewer v-if="imgViewVisible" :url-list="[imageUrl]" @close="imgViewVisible = false" teleported />
  </div>
</template>

<style scoped lang="scss">
.is-error {
  .upload {
    :deep(.el-upload),
    :deep(.el-upload-dragger) {
      border: 1px dashed var(--el-color-danger) !important;

      &:hover {
        border-color: var(--el-color-primary) !important;
      }
    }
  }
}

:deep(.disabled) {
  .el-upload,
  .el-upload-dragger {
    cursor: not-allowed !important;
    background: var(--el-disabled-bg-color);
    border: 1px dashed var(--el-border-color-darker) !important;

    &:hover {
      border: 1px dashed var(--el-border-color-darker) !important;
    }
  }
}

.upload-box {
  .no-border {
    :deep(.el-upload) {
      border: none !important;
    }
  }

  :deep(.upload) {
    .el-upload {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: v-bind(width);
      height: v-bind(height);
      overflow: hidden;
      border: 1px dashed var(--el-border-color-darker);
      border-radius: v-bind(borderRadius);
      transition: var(--el-transition-duration-fast);

      &:hover {
        border-color: var(--el-color-primary);

        .upload-handle {
          opacity: 1;
        }
      }

      .el-upload-dragger {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        padding: 0;
        overflow: hidden;
        background-color: transparent;
        border: 1px dashed var(--el-border-color-darker);
        border-radius: v-bind(borderRadius);
        transition: var(--el-transition-duration-fast);

        &:hover {
          border: 1px dashed var(--el-color-primary);
        }
      }

      .el-upload-dragger.is-dragover {
        background-color: var(--el-color-primary-light-9);
        border: 2px dashed var(--el-color-primary) !important;
      }

      .upload-image {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      .upload-empty {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        line-height: 30px;
        color: var(--el-color-info);

        .el-icon {
          font-size: 28px;
          color: var(--el-text-color-secondary);
        }
      }

      .upload-handle {
        position: absolute;
        top: 0;
        right: 0;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        background: rgb(0 0 0 / 60%);
        border-radius: v-bind(borderRadius);
        opacity: 0;
        transition: var(--el-transition-duration-fast);

        .handle-icon {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          min-width: 40px;
          padding: 4px;
          margin: 0;
          color: #fff;
          border-radius: 6px;
          transition: all 0.2s ease;

          &:hover {
            background: rgb(255 255 255 / 20%);
            transform: translateY(-2px);
          }

          .el-icon {
            margin-bottom: 4px;
            font-size: 16px;
            line-height: 1;
          }

          span {
            font-size: 12px;
            line-height: 1;
            white-space: nowrap;
          }
        }
      }
    }
  }

  .el-upload__tip {
    line-height: 18px;
    text-align: center;
  }
}
</style>
