<script setup lang="ts" name="UploadImgMultiple">
import type { UploadRequestOptions } from 'element-plus';

import type { MultipleImageUploadProps } from './types';

import { computed, inject, ref } from 'vue';

import { uploadImageApi } from '@vben/api';

import { ElIcon, ElImageViewer, ElNotification, ElUpload, formContextKey, formItemContextKey } from 'element-plus';

import { useCosUpload } from './hooks/useCosUpload';
import { useUpload } from './hooks/useUpload';

// 接受父组件参数
const props = withDefaults(defineProps<MultipleImageUploadProps>(), {
  modelValue: () => [],
  maxCount: 9,
  drag: true,
  disabled: false,
  fileSize: 5,
  fileType: () => ['image/jpeg', 'image/png', 'image/gif'],
  itemSize: '120px',
  sortable: false,
  useCos: false,
});

const emit = defineEmits<{
  'update:modelValue': [value: string[]];
}>();

// 获取表单上下文
const formContext = inject(formContextKey, void 0);
const formItemContext = inject(formItemContextKey, void 0);

// 判断是否禁用
const isDisabled = computed(() => {
  return props.disabled || formContext?.disabled;
});

// 是否达到最大上传数量
const isMaxCount = computed(() => {
  return props.modelValue.length >= props.maxCount;
});

// 图片预览
const previewVisible = ref(false);
const previewIndex = ref(0);

// 上传队列跟踪
const uploadingCount = ref(0);

// 使用上传hooks
const { beforeUpload, generateId } = useUpload({
  fileSize: props.fileSize,
  fileType: props.fileType,
});

const { cosUpload } = useCosUpload();

/**
 * 处理文件上传
 */
const handleUpload = async (options: UploadRequestOptions) => {
  // 检查图片数量限制（包括正在上传的图片）
  const totalCount = props.modelValue.length + uploadingCount.value;
  if (totalCount >= props.maxCount) {
    ElNotification({
      title: '温馨提示',
      message: `最多只能上传 ${props.maxCount} 张图片！`,
      type: 'warning',
    });
    options.onError(new Error('图片数量超出限制') as any);
    return;
  }

  // 增加上传计数
  uploadingCount.value++;

  try {
    let imageUrl: string;

    if (props.useCos) {
      // 使用COS直传
      imageUrl = await cosUpload(options.file, props.cosParams);
    } else {
      // 使用普通上传
      const api = props.api ?? uploadImageApi;
      imageUrl = await api({ file: options.file });
    }

    // 更新图片列表
    const newImages = [...props.modelValue, imageUrl];
    emit('update:modelValue', newImages);

    // 触发表单验证
    if (formItemContext?.prop && formContext?.validateField) {
      formContext.validateField([formItemContext.prop as string]);
    }
  } catch (error) {
    options.onError(error as any);
  } finally {
    // 减少上传计数
    uploadingCount.value--;
  }
};

/**
 * 删除图片
 */
const removeImage = (index: number) => {
  const newImages = props.modelValue.filter((_, i) => i !== index);
  emit('update:modelValue', newImages);
};

/**
 * 预览图片
 */
const previewImage = (index: number) => {
  previewIndex.value = index;
  previewVisible.value = true;
};

/**
 * 图片排序（如果启用）
 */
const moveImage = (fromIndex: number, toIndex: number) => {
  if (!props.sortable) return;

  const newImages = [...props.modelValue];
  const [movedImage] = newImages.splice(fromIndex, 1);
  if (movedImage) {
    newImages.splice(toIndex, 0, movedImage);
    emit('update:modelValue', newImages);
  }
};

// 生成唯一ID
const uploadId = generateId();
</script>

<template>
  <div class="upload-multiple">
    <!-- 已上传的图片列表 -->
    <div class="image-list">
      <div v-for="(imageUrl, index) in modelValue" :key="`img-${index}`" class="image-item" :class="{ sortable }">
        <img :src="imageUrl" class="image" @click="previewImage(index)" />

        <!-- 操作按钮 -->
        <div class="image-actions" @click.stop>
          <div class="action-btn" @click="previewImage(index)">
            <ElIcon><ZoomIn /></ElIcon>
          </div>
          <div v-if="!isDisabled" class="action-btn" @click="removeImage(index)">
            <ElIcon><Delete /></ElIcon>
          </div>
          <!-- 排序按钮 -->
          <div v-if="sortable && !isDisabled && index > 0" class="action-btn" @click="moveImage(index, index - 1)">
            <ElIcon><ArrowUp /></ElIcon>
          </div>
          <div
            v-if="sortable && !isDisabled && index < modelValue.length - 1"
            class="action-btn"
            @click="moveImage(index, index + 1)"
          >
            <ElIcon><ArrowDown /></ElIcon>
          </div>
        </div>
      </div>

      <!-- 上传按钮 -->
      <div v-if="!isMaxCount" class="upload-item">
        <ElUpload
          :id="uploadId"
          action="#"
          class="uploader"
          :class="{ disabled: isDisabled }"
          :multiple="true"
          :disabled="isDisabled"
          :show-file-list="false"
          :http-request="handleUpload"
          :before-upload="beforeUpload"
          :drag="drag"
          :accept="fileType.join(',')"
        >
          <div class="upload-trigger">
            <ElIcon class="upload-icon"><Plus /></ElIcon>
            <div class="upload-text">上传图片</div>
          </div>
        </ElUpload>
      </div>
    </div>

    <!-- 提示信息 -->
    <div class="upload-tip">
      <slot name="tip">
        <span>最多可上传 {{ maxCount }} 张图片，单张图片不超过 {{ fileSize }}MB</span>
      </slot>
    </div>

    <!-- 图片预览 -->
    <ElImageViewer
      v-if="previewVisible"
      :url-list="modelValue"
      :initial-index="previewIndex"
      @close="previewVisible = false"
      teleported
    />
  </div>
</template>

<style scoped lang="scss">
.upload-multiple {
  .image-list {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
  }

  .image-item {
    position: relative;
    width: v-bind(itemSize);
    height: v-bind(itemSize);
    overflow: hidden;
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--el-color-primary);

      .image-actions {
        opacity: 1;
      }
    }

    &.sortable {
      cursor: move;
    }

    .image {
      width: 100%;
      height: 100%;
      cursor: pointer;
      object-fit: cover;
    }

    .image-actions {
      position: absolute;
      inset: 0;
      display: flex;
      gap: 8px;
      align-items: center;
      justify-content: center;
      background: rgb(0 0 0 / 60%);
      opacity: 0;
      transition: opacity 0.3s ease;

      .action-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        color: white;
        cursor: pointer;
        background: rgb(255 255 255 / 20%);
        border-radius: 4px;
        transition: all 0.2s ease;

        &:hover {
          background: rgb(255 255 255 / 30%);
          transform: scale(1.1);
        }

        .el-icon {
          font-size: 16px;
        }
      }
    }
  }

  .upload-item {
    width: v-bind(itemSize);
    height: v-bind(itemSize);

    .uploader {
      width: 100%;
      height: 100%;

      :deep(.el-upload) {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        border: 1px dashed var(--el-border-color-darker);
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
          border-color: var(--el-color-primary);
        }

        &.disabled {
          cursor: not-allowed;
          background: var(--el-disabled-bg-color);
          border-color: var(--el-border-color-darker);

          &:hover {
            border-color: var(--el-border-color-darker);
          }
        }
      }

      :deep(.el-upload-dragger) {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        padding: 0;
        background: transparent;
        border: none;

        &:hover {
          background: var(--el-color-primary-light-9);
        }
      }
    }

    .upload-trigger {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: var(--el-text-color-secondary);

      .upload-icon {
        margin-bottom: 8px;
        font-size: 24px;
      }

      .upload-text {
        font-size: 12px;
      }
    }
  }

  .upload-tip {
    margin-top: 8px;
    font-size: 12px;
    line-height: 1.4;
    color: var(--el-text-color-regular);
  }
}

:deep(.disabled) {
  .el-upload,
  .el-upload-dragger {
    cursor: not-allowed !important;
    background: var(--el-disabled-bg-color);
    border-color: var(--el-border-color-darker) !important;

    &:hover {
      border-color: var(--el-border-color-darker) !important;
    }
  }
}
</style>
