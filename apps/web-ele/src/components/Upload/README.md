# Upload 上传组件

基于 Element Plus 二次封装的上传组件集合，支持图片上传、文件上传、多文件上传等多种场景，并支持 COS 直传。

## 组件列表

- `UploadImg` - 单图上传
- `UploadImgMultiple` - 多图上传
- `UploadFile` - 单文件上传
- `UploadFileMultiple` - 多文件上传
- `UploadAvatar` - 头像上传

## 基础用法

### 单图上传

```vue
<template>
  <UploadImg v-model:imageUrl="form.cover" />
</template>

<script setup>
import { ref } from 'vue';
import { UploadImg } from '@/components/Upload';

const form = ref({
  cover: '',
});
</script>
```

### 多图上传

```vue
<template>
  <UploadImgMultiple v-model="form.images" :max-count="5" :sortable="true" />
</template>

<script setup>
import { ref } from 'vue';
import { UploadImgMultiple } from '@/components/Upload';

const form = ref({
  images: [],
});
</script>
```

### 文件上传

```vue
<template>
  <UploadFile v-model:fileUrl="form.documentUrl" v-model:fileName="form.documentName" accept=".pdf,.doc,.docx" />
</template>

<script setup>
import { ref } from 'vue';
import { UploadFile } from '@/components/Upload';

const form = ref({
  documentUrl: '',
  documentName: '',
});
</script>
```

### 多文件上传

```vue
<template>
  <UploadFileMultiple v-model="form.documents" :max-count="5" accept=".pdf,.doc,.docx,.xls,.xlsx" />
</template>

<script setup>
import { ref } from 'vue';
import { UploadFileMultiple } from '@/components/Upload';

const form = ref({
  documents: [],
});
</script>
```

### 头像上传

```vue
<template>
  <UploadAvatar v-model:avatar="form.avatar" size="120px" :circle="true" />
</template>

<script setup>
import { ref } from 'vue';
import { UploadAvatar } from '@/components/Upload';

const form = ref({
  avatar: '',
});
</script>
```

## COS 直传

现在使用[腾讯云官方 COS JS SDK](https://github.com/tencentyun/cos-js-sdk-v5)，更加稳定可靠。

### 使用方法

```vue
<template>
  <UploadImg
    v-model:imageUrl="form.cover"
    :use-cos="true"
    :cos-params="{
      path: 'images',
      duration: 3600,
    }"
  />
</template>
```

### 优势特性

1. **官方SDK** - 使用腾讯云官方维护的 COS JS SDK v5
2. **自动分片** - 大文件自动分片上传，提高成功率
3. **断点续传** - 支持上传中断后继续上传
4. **进度监控** - 精确的上传进度反馈
5. **错误重试** - 自动重试机制，提高上传成功率

### 后端要求

后端需要实现 `/upload/cos-credentials` 接口，返回临时密钥：

```typescript
{
  bucket: string;           // 存储桶名称
  region: string;           // 地域
  cdn: string;              // CDN域名
  credentials: {
    sessionToken: string;   // 临时令牌
    tmpSecretId: string;    // 临时密钥ID
    tmpSecretKey: string;   // 临时密钥Key
  };
  policy: {
    uploadPath: string;         // 上传路径
    filenameHash: boolean;      // 是否哈希文件名
    allowedExtensions: string[]; // 允许的扩展名
    maxFileSize: number;        // 最大文件大小
    overwrite: boolean;         // 是否允许覆盖
  };
}
```

### 配置要求

1. **CORS配置** - 确保COS存储桶配置了跨域访问规则
2. **临时密钥权限** - 确保临时密钥有 `cos:PutObject` 权限
3. **网络环境** - 确保前端可以访问COS服务

### 故障排除

如果COS直传失败，请检查：

1. 后端临时密钥服务是否正常
2. 返回的凭证格式是否正确
3. COS存储桶CORS配置是否正确
4. 查看浏览器控制台的详细错误信息

## API

### UploadImg Props

| 参数         | 说明             | 类型     | 默认值                                   |
| ------------ | ---------------- | -------- | ---------------------------------------- |
| imageUrl     | 图片地址         | string   | ''                                       |
| drag         | 是否支持拖拽     | boolean  | true                                     |
| disabled     | 是否禁用         | boolean  | false                                    |
| fileSize     | 文件大小限制(MB) | number   | 5                                        |
| fileType     | 文件类型限制     | string[] | ['image/jpeg', 'image/png', 'image/gif'] |
| height       | 组件高度         | string   | '150px'                                  |
| width        | 组件宽度         | string   | '150px'                                  |
| borderRadius | 边框圆角         | string   | '8px'                                    |
| api          | 上传API方法      | Function | uploadImageApi                           |
| useCos       | 是否使用COS直传  | boolean  | false                                    |
| cosParams    | COS配置参数      | object   | -                                        |

### UploadImgMultiple Props

| 参数       | 说明             | 类型     | 默认值                                   |
| ---------- | ---------------- | -------- | ---------------------------------------- |
| modelValue | 图片地址数组     | string[] | []                                       |
| maxCount   | 最大上传数量     | number   | 9                                        |
| drag       | 是否支持拖拽     | boolean  | true                                     |
| disabled   | 是否禁用         | boolean  | false                                    |
| fileSize   | 文件大小限制(MB) | number   | 5                                        |
| fileType   | 文件类型限制     | string[] | ['image/jpeg', 'image/png', 'image/gif'] |
| itemSize   | 单个图片容器尺寸 | string   | '120px'                                  |
| sortable   | 是否显示排序功能 | boolean  | false                                    |
| api        | 上传API方法      | Function | uploadImageApi                           |
| useCos     | 是否使用COS直传  | boolean  | false                                    |
| cosParams  | COS配置参数      | object   | -                                        |

### UploadFile Props

| 参数      | 说明             | 类型     | 默认值         |
| --------- | ---------------- | -------- | -------------- |
| fileUrl   | 文件地址         | string   | ''             |
| fileName  | 文件名           | string   | ''             |
| accept    | 文件类型限制     | string   | ''             |
| drag      | 是否支持拖拽     | boolean  | true           |
| disabled  | 是否禁用         | boolean  | false          |
| fileSize  | 文件大小限制(MB) | number   | 10             |
| api       | 上传API方法      | Function | uploadImageApi |
| useCos    | 是否使用COS直传  | boolean  | false          |
| cosParams | COS配置参数      | object   | -              |

### UploadFileMultiple Props

| 参数       | 说明             | 类型         | 默认值         |
| ---------- | ---------------- | ------------ | -------------- |
| modelValue | 文件列表         | UploadFile[] | []             |
| maxCount   | 最大上传数量     | number       | 10             |
| accept     | 文件类型限制     | string       | ''             |
| drag       | 是否支持拖拽     | boolean      | true           |
| disabled   | 是否禁用         | boolean      | false          |
| fileSize   | 文件大小限制(MB) | number       | 10             |
| api        | 上传API方法      | Function     | uploadImageApi |
| useCos     | 是否使用COS直传  | boolean      | false          |
| cosParams  | COS配置参数      | object       | -              |

### UploadAvatar Props

| 参数      | 说明             | 类型     | 默认值                      |
| --------- | ---------------- | -------- | --------------------------- |
| avatar    | 头像地址         | string   | ''                          |
| size      | 头像尺寸         | string   | '100px'                     |
| circle    | 是否圆形         | boolean  | true                        |
| disabled  | 是否禁用         | boolean  | false                       |
| fileSize  | 文件大小限制(MB) | number   | 2                           |
| fileType  | 文件类型限制     | string[] | ['image/jpeg', 'image/png'] |
| api       | 上传API方法      | Function | uploadImageApi              |
| useCos    | 是否使用COS直传  | boolean  | false                       |
| cosParams | COS配置参数      | object   | -                           |

## Events

### UploadImg Events

| 事件名          | 说明         | 参数            |
| --------------- | ------------ | --------------- |
| update:imageUrl | 图片地址更新 | (value: string) |

### UploadImgMultiple Events

| 事件名            | 说明         | 参数              |
| ----------------- | ------------ | ----------------- |
| update:modelValue | 图片列表更新 | (value: string[]) |

### UploadFile Events

| 事件名          | 说明         | 参数            |
| --------------- | ------------ | --------------- |
| update:fileUrl  | 文件地址更新 | (value: string) |
| update:fileName | 文件名更新   | (value: string) |

### UploadFileMultiple Events

| 事件名            | 说明         | 参数                  |
| ----------------- | ------------ | --------------------- |
| update:modelValue | 文件列表更新 | (value: UploadFile[]) |

### UploadAvatar Events

| 事件名        | 说明         | 参数            |
| ------------- | ------------ | --------------- |
| update:avatar | 头像地址更新 | (value: string) |

## Slots

### tip

自定义提示信息

```vue
<UploadImg v-model:imageUrl="form.cover">
  <template #tip>
    <div>请上传 JPG/PNG 格式的图片，大小不超过 2MB</div>
  </template>
</UploadImg>
```

### empty (仅 UploadImg)

自定义空状态

```vue
<UploadImg v-model:imageUrl="form.cover">
  <template #empty>
    <div>点击上传图片</div>
  </template>
</UploadImg>
```

## Hooks

### useUpload

通用上传逻辑 Hook

```typescript
import { useUpload } from '@/components/Upload';

const { uploading, uploadProgress, beforeUpload, handleUpload } = useUpload({
  fileSize: 5,
  fileType: ['image/jpeg', 'image/png'],
  onSuccess: (response, file) => {
    console.log('上传成功', response);
  },
  onError: (error, file) => {
    console.log('上传失败', error);
  },
});
```

### useCosUpload

COS 直传 Hook（使用官方SDK）

```typescript
import { useCosUpload } from '@/components/Upload';

const { uploading, uploadProgress, cosUpload } = useCosUpload();

// 上传文件
const uploadFile = async (file: File) => {
  try {
    const url = await cosUpload(file, {
      path: 'images',
      duration: 3600,
    });
    console.log('上传成功', url);
  } catch (error) {
    console.log('上传失败', error);
  }
};
```
