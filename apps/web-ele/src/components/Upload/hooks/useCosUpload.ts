import type { CosUploadOptions } from '../types';

import { ref } from 'vue';

import { cosCredentialsApi } from '@vben/api';

import COS from 'cos-js-sdk-v5';
import { ElNotification } from 'element-plus';

/**
 * COS直传上传Hook - 使用官方SDK
 */
export function useCosUpload() {
  const uploading = ref(false);
  const uploadProgress = ref(0);

  /**
   * 获取COS临时密钥
   */
  const getCosCredentials = async (params?: { duration?: number; path?: string; prefixes?: string[] }) => {
    try {
      const credentials = await cosCredentialsApi(params);
      console.warn('获取到的COS凭证:', credentials);
      return credentials;
    } catch (error) {
      console.error('获取COS凭证失败:', error);
      ElNotification({
        title: '获取上传凭证失败',
        message: '请稍后重试',
        type: 'error',
      });
      throw error;
    }
  };

  /**
   * 生成文件名
   */
  const generateFileName = (file: File, filenameHash: boolean, uploadPath: string): string => {
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).slice(2, 8);
    const ext = file.name.split('.').pop() || '';

    // 确保上传路径格式正确，去除开头和结尾的斜杠
    const cleanPath = uploadPath.replaceAll(/^\/+|\/+$/g, '');

    if (filenameHash) {
      return cleanPath ? `${cleanPath}/${timestamp}_${randomStr}.${ext}` : `${timestamp}_${randomStr}.${ext}`;
    }

    const originalName = file.name.replace(/\.[^/.]+$/, '');
    return cleanPath ? `${cleanPath}/${originalName}_${timestamp}.${ext}` : `${originalName}_${timestamp}.${ext}`;
  };

  /**
   * 创建COS实例
   */
  const createCosInstance = (credentials: any) => {
    // 兼容不同的凭证数据结构
    const credData = credentials.credentials || credentials;
    const { tmpSecretId, tmpSecretKey, sessionToken } = credData;

    // 使用根级别的时间戳（后端返回的）
    const { startTime, expiredTime } = credentials;

    console.warn('COS凭证信息:', {
      tmpSecretId,
      startTime,
      expiredTime,
      hasSessionToken: !!sessionToken,
      currentTime: Math.floor(Date.now() / 1000),
    });

    return new COS({
      getAuthorization: (_options, callback) => {
        // 使用后端返回的时间戳，而不是当前时间
        callback({
          TmpSecretId: tmpSecretId,
          TmpSecretKey: tmpSecretKey,
          SecurityToken: sessionToken,
          StartTime: startTime,
          ExpiredTime: expiredTime,
        });
      },
    });
  };

  /**
   * 上传文件到COS - 使用官方SDK
   */
  const uploadToCos = async (options: CosUploadOptions): Promise<string> => {
    const { file, credentials, onProgress } = options;
    const { bucket, region, cdn, policy } = credentials;

    return new Promise((resolve, reject) => {
      try {
        uploading.value = true;
        uploadProgress.value = 0;

        // 生成文件名 - 确保路径格式正确
        const fileName = generateFileName(file, policy.filenameHash, policy.uploadPath);
        // 确保文件名不以 / 开头，COS Key 不应该以 / 开头
        const cosKey = fileName.startsWith('/') ? fileName.slice(1) : fileName;

        // 创建COS实例
        const cos = createCosInstance(credentials);

        // 使用官方SDK上传
        cos.uploadFile(
          {
            Bucket: bucket,
            Region: region,
            Key: cosKey,
            Body: file,
            SliceSize: 1024 * 1024, // 大于1MB才进行分块上传
            onProgress: (progressData) => {
              const progress = Math.round(progressData.percent * 100);
              uploadProgress.value = progress;
              onProgress?.(progress);
              console.warn('上传进度:', `${progress}%`);
            },
          },
          (err, _data) => {
            if (err) {
              reject(new Error(`文件上传失败: ${err.message || err}`));
            } else {
              // 确保CDN URL格式正确
              const fileUrl = cdn.endsWith('/') ? `${cdn}${cosKey}` : `${cdn}/${cosKey}`;
              resolve(fileUrl);
              ElNotification({
                title: '上传成功',
                message: '文件上传完成',
                type: 'success',
              });
            }
          },
        );
      } catch (error) {
        reject(error);
      } finally {
        uploading.value = false;
        uploadProgress.value = 0;
      }
    });
  };

  /**
   * 完整的COS上传流程
   */
  const cosUpload = async (
    file: File,
    cosParams?: {
      duration?: number;
      path?: string;
      prefixes?: string[];
    },
    onProgress?: (progress: number) => void,
  ): Promise<string> => {
    try {
      // 1. 获取COS临时密钥
      const credentials = await getCosCredentials(cosParams);

      // 2. 验证文件
      if (file.size > credentials.policy.maxFileSize) {
        throw new Error(`文件大小超过限制 ${credentials.policy.maxFileSize / 1024 / 1024}MB`);
      }

      const fileExt = file.name.split('.').pop()?.toLowerCase() || '';
      if (credentials.policy.allowedExtensions.length > 0 && !credentials.policy.allowedExtensions.includes(fileExt)) {
        throw new Error(`不支持的文件类型: ${fileExt}`);
      }

      // 3. 上传到COS
      const fileUrl = await uploadToCos({
        file,
        credentials,
        onProgress,
      });

      return fileUrl;
    } catch (error) {
      ElNotification({
        title: '上传失败',
        message: error instanceof Error ? error.message : '未知错误',
        type: 'error',
      });
      throw error;
    }
  };

  return {
    uploading,
    uploadProgress,
    getCosCredentials,
    uploadToCos,
    cosUpload,
  };
}
