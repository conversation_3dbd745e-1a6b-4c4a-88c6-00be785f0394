import type { UploadRequestOptions } from 'element-plus';

import type { UploadHookOptions } from '../types';

import { inject, ref } from 'vue';

import { uploadImageApi } from '@vben/api';

import { ElNotification, formContextKey, formItemContextKey } from 'element-plus';

/**
 * 通用上传逻辑Hook
 */
export function useUpload(options: UploadHookOptions = {}) {
  const { api = uploadImageApi, fileSize = 5, fileType = [], accept, onSuccess, onError, onProgress } = options;

  // 获取表单上下文
  const formContext = inject(formContextKey, void 0);
  const formItemContext = inject(formItemContextKey, void 0);

  // 上传状态
  const uploading = ref(false);
  const uploadProgress = ref(0);

  /**
   * 统一处理文件类型验证数组
   */
  const getFileTypeArray = (): string[] => {
    // 优先使用 fileType 数组
    if (fileType && fileType.length > 0) {
      return fileType;
    }

    // 其次使用 accept 字符串转换为数组
    if (accept) {
      return accept.split(',').map((type) => type.trim());
    }

    // 都没有则返回空数组（不限制类型）
    return [];
  };

  /**
   * 文件上传前的验证
   */
  const beforeUpload = (rawFile: File): boolean => {
    // 文件大小验证
    const sizeValid = rawFile.size / 1024 / 1024 < fileSize;
    if (!sizeValid) {
      ElNotification({
        title: '温馨提示',
        message: `上传文件大小不能超过 ${fileSize}MB！`,
        type: 'warning',
      });
      return false;
    }

    // 文件类型验证 - 支持扩展名和MIME类型混合验证
    const typeArray = getFileTypeArray();
    if (typeArray.length > 0) {
      const fileExt = `.${rawFile.name.split('.').pop()?.toLowerCase()}`;
      const mimeType = rawFile.type;

      console.warn('文件类型验证:', {
        typeArray,
        fileName: rawFile.name,
        fileExt,
        mimeType,
      });

      const isValidType = typeArray.some((type) => {
        // 如果是扩展名格式（如 .doc, .pdf）
        if (type.startsWith('.')) {
          return type.toLowerCase() === fileExt.toLowerCase();
        }

        // 如果是 MIME 类型格式
        if (type.includes('/')) {
          // 处理通配符（如 image/*）
          if (type.endsWith('/*')) {
            const baseType = type.replace('/*', '');
            return mimeType.startsWith(`${baseType}/`);
          }
          // 精确匹配 MIME 类型
          return type === mimeType;
        }

        // 其他情况，尝试匹配文件扩展名
        return type.toLowerCase() === fileExt.toLowerCase().replace('.', '');
      });

      if (!isValidType) {
        ElNotification({
          title: '温馨提示',
          message: '上传文件不符合所需的格式！',
          type: 'warning',
        });
        return false;
      }
    }

    return true;
  };

  /**
   * 处理文件上传
   */
  const handleUpload = async (uploadOptions: UploadRequestOptions): Promise<void> => {
    try {
      uploading.value = true;
      uploadProgress.value = 0;

      // 模拟进度更新
      const progressTimer = setInterval(() => {
        if (uploadProgress.value < 90) {
          uploadProgress.value += 10;
          onProgress?.(uploadProgress.value, uploadOptions.file);
        }
      }, 100);

      const response = await api({ file: uploadOptions.file });

      clearInterval(progressTimer);
      uploadProgress.value = 100;

      // 触发成功回调
      onSuccess?.(response, uploadOptions.file);

      // 触发表单验证
      if (formItemContext?.prop && formContext?.validateField) {
        formContext.validateField([formItemContext.prop as string]);
      }

      ElNotification({
        title: '温馨提示',
        message: '文件上传成功！',
        type: 'success',
      });
    } catch (error) {
      onError?.(error, uploadOptions.file);
      uploadOptions.onError(error as any);

      ElNotification({
        title: '温馨提示',
        message: '文件上传失败，请重新上传！',
        type: 'error',
      });
    } finally {
      uploading.value = false;
      uploadProgress.value = 0;
    }
  };

  /**
   * 生成唯一ID
   */
  const generateId = (): string => {
    return `upload-${Date.now()}-${Math.random().toString(36).slice(2, 9)}`;
  };

  return {
    uploading,
    uploadProgress,
    beforeUpload,
    handleUpload,
    generateId,
  };
}
