export { default as UploadAvatar } from './Avatar.vue';
export { default as UploadFile } from './File.vue';
export { default as UploadFileMultiple } from './FileMultiple.vue';
export { useCosUpload } from './hooks/useCosUpload';
// Hooks导出
export { useUpload } from './hooks/useUpload';

// 组件导出
export { default as UploadImg } from './Img.vue';
export { default as UploadImgMultiple } from './ImgMultiple.vue';

// 类型导出
export type {
  AvatarUploadProps,
  BaseUploadProps,
  CosUploadOptions,
  FileUploadProps,
  ImageUploadProps,
  MultipleFileUploadProps,
  MultipleImageUploadProps,
  UploadFile as UploadFileType,
  UploadHookOptions,
} from './types';
