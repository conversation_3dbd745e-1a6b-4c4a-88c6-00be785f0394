export interface BaseUploadProps {
  /** 是否禁用 */
  disabled?: boolean;
  /** 文件大小限制(MB) */
  fileSize?: number;
  /** 上传API方法 */
  api?: (params: any) => Promise<any>;
  /** 是否使用COS直传 */
  useCos?: boolean;
  /** COS配置参数 */
  cosParams?: {
    duration?: number;
    path?: string;
    prefixes?: string[];
  };
}

export interface ImageUploadProps extends BaseUploadProps {
  /** 图片地址 */
  imageUrl?: string;
  /** 是否支持拖拽 */
  drag?: boolean;
  /** 图片类型限制 */
  fileType?: string[];
  /** 组件高度 */
  height?: string;
  /** 组件宽度 */
  width?: string;
  /** 边框圆角 */
  borderRadius?: string;
}

export interface MultipleImageUploadProps extends BaseUploadProps {
  /** 图片地址数组 */
  modelValue?: string[];
  /** 最大上传数量 */
  maxCount?: number;
  /** 是否支持拖拽 */
  drag?: boolean;
  /** 图片类型限制 */
  fileType?: string[];
  /** 单个图片容器尺寸 */
  itemSize?: string;
  /** 是否显示排序功能 */
  sortable?: boolean;
}

export interface UploadFile {
  /** 文件名 */
  name: string;
  /** 文件地址 */
  url: string;
  /** 文件大小 */
  size?: number;
  /** 文件类型 */
  type?: string;
  /** 上传状态 */
  status?: 'error' | 'success' | 'uploading';
  /** 上传进度 */
  progress?: number;
  /** 唯一标识 */
  uid?: string;
}

export interface FileUploadProps extends BaseUploadProps {
  /** 文件地址 */
  fileUrl?: string;
  /** 文件名 */
  fileName?: string;
  /** 文件类型限制 */
  accept?: string;
  /** 是否支持拖拽 */
  drag?: boolean;
}

export interface MultipleFileUploadProps extends BaseUploadProps {
  /** 文件列表 */
  modelValue?: UploadFile[];
  /** 最大上传数量 */
  maxCount?: number;
  /** 文件类型限制 */
  accept?: string;
  /** 是否支持拖拽 */
  drag?: boolean;
}

export interface AvatarUploadProps extends BaseUploadProps {
  /** 头像地址 */
  avatar?: string;
  /** 头像尺寸 */
  size?: string;
  /** 是否圆形 */
  circle?: boolean;
  /** 图片类型限制 */
  fileType?: string[];
}

export interface UploadHookOptions extends BaseUploadProps {
  /** 文件类型限制 */
  fileType?: string[];
  /** 接受的文件类型 */
  accept?: string;
  /** 是否多选 */
  multiple?: boolean;
  /** 最大数量 */
  maxCount?: number;
  /** 成功回调 */
  onSuccess?: (response: any, file: File) => void;
  /** 错误回调 */
  onError?: (error: any, file: File) => void;
  /** 进度回调 */
  onProgress?: (progress: number, file: File) => void;
}

export interface CosUploadOptions {
  /** 文件对象 */
  file: File;
  /** COS配置 */
  credentials: {
    authorization?: string;
    bucket: string;
    cdn: string;
    cosHost?: string;
    // 根据官方文档添加的字段
    cosKey?: string;
    credentials: {
      sessionToken: string;
      tmpSecretId: string;
      tmpSecretKey: string;
    };
    policy: {
      allowedExtensions: string[];
      filenameHash: boolean;
      maxFileSize: number;
      overwrite: boolean;
      uploadPath: string;
    };
    region: string;
  };
  /** 进度回调 */
  onProgress?: (progress: number) => void;
}
