import type { Permission } from '@vben/api';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn } from '#/adapter/vxe-table';

import { z } from '#/adapter/form';

/**
 * 获取编辑表单的字段配置。如果没有使用多语言，可以直接export一个数组常量
 */
export function useSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'brand_name',
      label: '品牌名称',
      rules: z.string().min(2, '品牌名称长度不能少于2个字符').max(20, '品牌名称长度不能超过20个字符'),
    },
    {
      component: 'UploadImg',
      modelPropName: 'imageUrl',
      componentProps: {
        useCos: true,
      },
      fieldName: 'logo',
      label: '品牌 LOGO',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 2 },
        ],
        optionType: 'button',
      },
      defaultValue: 1,
      fieldName: 'status',
      label: '状态',
    },
    {
      component: 'Input',
      componentProps: {
        type: 'textarea',
        rows: 3,
        showCount: true,
        maxlength: 50,
      },
      fieldName: 'description',
      label: '备注',
      rules: z.string().max(50, '备注长度不能超过50个字符').optional(),
    },
    {
      component: 'InputNumber',
      defaultValue: 100,
      fieldName: 'sort',
      label: '排序',
      rules: z.number().min(1, '排序必须大于0').max(99_999, '排序不能超过99999'),
    },
  ];
}

/**
 * 获取表格列配置
 * @description 使用函数的形式返回列数据而不是直接export一个Array常量，是为了响应语言切换时重新翻译表头
 * @param onActionClick 表格操作按钮点击事件
 */
export function useColumns<T = Permission.Brand>(
  onActionClick?: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<any>,
): VxeTableGridOptions['columns'] {
  return [
    { type: 'checkbox', width: 60 },
    {
      field: 'id',
      title: 'ID',
      width: 80,
    },
    {
      align: 'center',
      field: 'brand_name',
      title: '品牌名称',
      width: 150,
    },
    {
      cellRender: {
        attrs: {
          style: 'height: 50px;',
        },
        name: 'CellImage',
      },
      field: 'logo',
      title: '品牌 LOGOa',
      width: 100,
    },
    {
      cellRender: {
        attrs: { beforeChange: onStatusChange },
        name: 'CellSwitch',
      },
      field: 'status',
      title: '状态',
      width: 100,
    },
    {
      field: 'created_at',
      title: '创建时间',
      width: 180,
      sortable: true,
    },
    {
      field: 'description',
      title: '备注',
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'brand_name',
          nameTitle: '品牌',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: ['edit', 'delete'],
      },
      field: 'operation',
      fixed: 'right',
      headerAlign: 'center',
      showOverflow: false,
      title: '操作',
      width: 200,
    },
  ];
}
