<script lang="ts" setup>
import type { Permission } from '@vben/api';

import type { OnActionClickParams, VxeTableGridOptions } from '#/adapter/vxe-table';

import { deleteBrandApi, getBrandListApi, updateBrandApi } from '@vben/api';
import { Page, useVbenModal } from '@vben/common-ui';
import { Plus } from '@vben/icons';

import { ElButton, ElMessage } from 'element-plus';

import { useVbenVxeGrid, vxeCheckboxChecked } from '#/adapter/vxe-table';
import { useHandleData } from '#/composables/useHandleData';

import { useColumns } from './data';
import Form from './modules/form.vue';

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
});

/**
 * 操作按钮点击事件
 * @param {OnActionClickParams<Permission.Brand>} param
 */
function onActionClick({ code, row }: OnActionClickParams<Permission.Brand>) {
  const actions: Record<string, () => void> = {
    edit: () => open(row),
    delete: () => onDelete(row),
  };

  actions[code]?.();
}

/**
 * 打开表单
 * @param row 品牌信息
 */
function open(row: Partial<Permission.Brand> = {}) {
  formModalApi.setData(row).open();
}

/**
 * 删除品牌
 * @param row
 */
async function onDelete(row: Permission.Brand) {
  await deleteBrandApi(row.id);
  ElMessage.success('删除成功');
  refreshGrid();
}

/**
 * 切换状态
 * @param newStatus 新状态
 * @param row 品牌信息
 */
function onStatusChange(newStatus: number, row: Permission.Brand) {
  return useHandleData(updateBrandApi, { id: row.id, data: { status: newStatus } }, `切换状态`);
}

function onBatchDelete() {
  console.warn('onBatchDelete', gridApi.grid.getCheckboxRecords());
}

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: useColumns(onActionClick, onStatusChange),
    proxyConfig: {
      ajax: {
        query: async ({ page, sort }) => {
          return await getBrandListApi({
            page: page.currentPage,
            limit: page.pageSize,
            sortField: sort.field,
            sortOrder: sort.order,
          });
        },
      },
    },
    showOverflow: false,
  } as VxeTableGridOptions,
});
</script>
<template>
  <Page auto-content-height>
    <FormModal @success="refreshGrid" />
    <Grid table-title="品牌列表">
      <template #toolbar-tools>
        <ElButton type="primary" @click="open">
          <Plus class="size-5" />
          新增品牌
        </ElButton>
        <ElButton type="danger" :disabled="!vxeCheckboxChecked(gridApi)" @click="onBatchDelete">
          <span class="icon-[mdi--trash-can-outline] size-5"></span>
          批量删除的
        </ElButton>
      </template>
    </Grid>
  </Page>
</template>
