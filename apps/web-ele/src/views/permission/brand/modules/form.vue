<script lang="ts" setup>
import type { Permission } from '@vben/api';

import { computed, ref } from 'vue';

import { createBrandApi, updateBrandApi } from '@vben/api';
import { useVbenModal } from '@vben/common-ui';

import { ElMessage } from 'element-plus';

import { useVbenForm } from '#/adapter/form';

import { useSchema } from '../data';

const emit = defineEmits(['success']);
const formData = ref<Permission.Brand>();
const getTitle = computed(() => {
  return formData.value?.id ? `编辑品牌` : `新增品牌`;
});

const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: useSchema(),
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  onConfirm: handleConfirm,
  onOpenChange(isOpen) {
    if (!isOpen) return;
    const data = modalApi.getData<Permission.Brand>();
    formData.value = data;
    formApi.setValues(formData.value);
  },
});

async function handleConfirm() {
  const { valid } = await formApi.validate();
  if (valid) {
    modalApi.lock();
    const data = await formApi.getValues();
    try {
      await (formData.value?.id ? updateBrandApi({ id: formData.value.id, ...data }) : createBrandApi(data));
      ElMessage.success(`${getTitle.value}成功`);
      modalApi.close();
      emit('success');
    } finally {
      modalApi.lock(false);
    }
  }
}
</script>

<template>
  <Modal :title="getTitle">
    <Form class="mx-4" />
  </Modal>
</template>
