import type { Permission } from '@vben/api';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn } from '#/adapter/vxe-table';

import { getDepartmentListApi } from '@vben/api';

import { z } from '#/adapter/form';

/**
 * 获取编辑表单的字段配置。如果没有使用多语言，可以直接export一个数组常量
 */
export function useSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'department_name',
      label: '部门名称',
      rules: z.string().min(2, '部门名称长度不能少于2个字符').max(20, '部门名称长度不能超过20个字符'),
    },
    {
      component: 'ApiTreeSelect',
      componentProps: {
        allowClear: true,
        api: getDepartmentListApi,
        class: 'w-full',
        labelField: 'department_name',
        valueField: 'id',
        childrenField: 'children',
        checkStrictly: true,
      },
      fieldName: 'parent_id',
      label: '上级部门',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 2 },
        ],
        optionType: 'button',
      },
      defaultValue: 1,
      fieldName: 'status',
      label: '状态',
    },
    {
      component: 'Input',
      componentProps: {
        type: 'textarea',
        rows: 3,
        showCount: true,
        maxlength: 50,
      },
      fieldName: 'remark',
      label: '备注',
      rules: z.string().max(50, '备注长度不能超过50个字符').optional(),
    },
  ];
}

/**
 * 获取表格列配置
 * @description 使用函数的形式返回列数据而不是直接export一个Array常量，是为了响应语言切换时重新翻译表头
 * @param onActionClick 表格操作按钮点击事件
 */
export function useColumns(
  onActionClick?: OnActionClickFn<Permission.Department>,
): VxeTableGridOptions<Permission.Department>['columns'] {
  return [
    {
      align: 'left',
      field: 'department_name',
      fixed: 'left',
      title: '部门名称',
      treeNode: true,
      width: 150,
    },
    {
      cellRender: { name: 'CellTag' },
      field: 'status',
      title: '状态',
      width: 100,
    },
    {
      field: 'created_at',
      title: '创建时间',
      width: 180,
    },
    {
      field: 'remark',
      title: '备注',
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'name',
          nameTitle: '名称',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'append',
            buttonText: '新增下级',
            icon: 'mdi:plus',
          },
          'edit',
          {
            code: 'delete',
            disabled: (row: Permission.Department) => {
              return !!(row.children && row.children.length > 0);
            },
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      headerAlign: 'center',
      showOverflow: false,
      title: '操作',
      width: 260,
    },
  ];
}
