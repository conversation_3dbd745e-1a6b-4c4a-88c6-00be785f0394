<script lang="ts" setup>
import type { Permission } from '@vben/api';

import type { OnActionClickParams, VxeTableGridOptions } from '#/adapter/vxe-table';

import { ref } from 'vue';

import { deleteDepartmentApi, getDepartmentListApi } from '@vben/api';
import { Page, useVbenModal } from '@vben/common-ui';
import { Plus } from '@vben/icons';

import { ElButton } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { useHandleData } from '#/composables/useHandleData';

import { useColumns } from './data';
import Form from './modules/form.vue';

// 展开状态管理
const isExpanded = ref(false);

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
});

/**
 * 打开表单
 * @param row 部门数据
 */
function onOpen(row: Partial<Permission.Department> = {}) {
  formModalApi.setData(row).open();
}

/**
 * 操作按钮点击事件
 * @param {OnActionClickParams<Permission.Brand>} param
 */
function onActionClick({ code, row }: OnActionClickParams<Permission.Department>) {
  const actions: Record<string, () => void> = {
    append: () => onOpen({ parent_id: row.id }),
    edit: () => onOpen(row),
    delete: () => onDelete(row),
  };

  actions[code]?.();
}

/**
 * 删除部门
 * @param row
 */
function onDelete(row: Permission.Department) {
  return useHandleData(deleteDepartmentApi, row.id, '删除信息');
}

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: useColumns(onActionClick),
    pagerConfig: { enabled: false },
    proxyConfig: {
      ajax: {
        query: async (_params, formValues) => {
          return await getDepartmentListApi(formValues);
        },
      },
    },
    treeConfig: {
      parentField: 'parent_id',
      rowField: 'id',
      transform: false,
    },
  } as VxeTableGridOptions,
});

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}

/**
 * 切换展开/折叠状态
 */
function toggleExpandAll() {
  isExpanded.value = !isExpanded.value;
  gridApi.grid?.setAllTreeExpand(isExpanded.value);
}
</script>
<template>
  <Page auto-content-height>
    <FormModal @success="refreshGrid" />
    <Grid table-title="部门列表">
      <template #toolbar-tools>
        <ElButton type="primary" @click="onOpen()">
          <Plus class="mr-1 size-5" />
          新增部门
        </ElButton>
        <span class="i-mdi-format-align-middle mr-1 size-5"></span>
        <ElButton type="default" @click="toggleExpandAll">
          <span v-if="!isExpanded" class="icon-[mdi--arrow-expand-vertical] mr-1 size-5"></span>
          <span v-else class="icon-[mdi--format-align-middle] mr-1 size-5"></span>
          {{ isExpanded ? '折叠所有' : '展开所有' }}
        </ElButton>
      </template>
    </Grid>
  </Page>
</template>
