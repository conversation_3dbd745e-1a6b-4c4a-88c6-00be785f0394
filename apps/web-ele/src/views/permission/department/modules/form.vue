<script lang="ts" setup>
import type { Permission } from '@vben/api';

import { computed, ref } from 'vue';

import { createDepartmentApi, updateDepartmentApi } from '@vben/api';
import { useVbenModal } from '@vben/common-ui';

import { ElMessage } from 'element-plus';

import { useVbenForm } from '#/adapter/form';

import { useSchema } from '../data';

const emit = defineEmits(['success']);
const formData = ref<Permission.Department>();
const getTitle = computed(() => {
  return formData.value?.id ? `编辑部门` : `新增部门`;
});

const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: useSchema(),
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (valid) {
      modalApi.lock();
      const data = await formApi.getValues();
      if (formData.value?.id && data.parent_id === formData.value?.id) {
        ElMessage.error('不能选择自己作为上级部门');
        modalApi.lock(false);
      }
      try {
        await (formData.value?.id ? updateDepartmentApi({ id: formData.value.id, ...data }) : createDepartmentApi(data));
        modalApi.close();
        emit('success');
      } finally {
        modalApi.lock(false);
      }
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<Permission.Department>();
      if (data) {
        if (data.parent_id === 0) {
          data.parent_id = undefined;
        }
        formData.value = data;
        formApi.setValues(formData.value);
      }
    }
  },
});
</script>

<template>
  <Modal :title="getTitle">
    <Form class="mx-4" />
  </Modal>
</template>
