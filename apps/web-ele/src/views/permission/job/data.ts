import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn } from '#/adapter/vxe-table';

import { z } from '#/adapter/form';

/**
 * 获取编辑表单的字段配置。如果没有使用多语言，可以直接export一个数组常量
 */
export function useSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'job_name',
      label: '岗位名称',
      rules: z.string().min(2, '岗位名称长度不能少于2个字符').max(20, '岗位名称长度不能超过20个字符'),
    },
    {
      component: 'Input',
      fieldName: 'coding',
      label: '岗位编码',
      rules: z.string().min(2, '岗位编码长度不能少于2个字符').max(20, '岗位编码长度不能超过20个字符'),
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: '正常', value: 1 },
          { label: '停用', value: 2 },
        ],
        optionType: 'button',
      },
      defaultValue: 1,
      fieldName: 'status',
      label: '状态',
    },
    {
      component: 'Input',
      componentProps: {
        type: 'textarea',
        rows: 3,
        showCount: true,
        maxlength: 100,
      },
      fieldName: 'description',
      label: '备注',
      rules: z.string().max(100, '备注长度不能超过100个字符').optional(),
    },
    {
      component: 'InputNumber',
      defaultValue: 100,
      fieldName: 'sort',
      label: '排序',
      rules: z.number().min(1, '排序必须大于0').max(99_999, '排序不能超过99999'),
    },
  ];
}

/**
 * 获取表格列配置
 * @description 使用函数的形式返回列数据而不是直接export一个Array常量，是为了响应语言切换时重新翻译表头
 * @param onActionClick 表格操作按钮点击事件
 */
export function useColumns<T = any>(
  onActionClick?: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<any>,
): VxeTableGridOptions['columns'] {
  return [
    { type: 'checkbox', width: 60 },
    {
      align: 'center',
      field: 'job_name',
      title: '岗位名称',
      width: 150,
    },
    {
      align: 'center',
      field: 'coding',
      title: '岗位编码',
      width: 120,
    },
    {
      cellRender: {
        attrs: { beforeChange: onStatusChange },
        name: 'CellSwitch',
      },
      field: 'status',
      title: '状态',
      width: 100,
    },
    {
      field: 'created_at',
      title: '创建时间',
      width: 180,
    },
    {
      field: 'description',
      title: '备注',
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'job_name',
          nameTitle: '岗位',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: ['edit', 'delete'],
      },
      field: 'operation',
      fixed: 'right',
      headerAlign: 'center',
      showOverflow: false,
      title: '操作',
      width: 200,
    },
  ];
}
