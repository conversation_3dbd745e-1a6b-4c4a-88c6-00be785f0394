<script lang="ts" setup>
import type { Permission } from '@vben/api';

import type { OnActionClickParams, VxeTableGridOptions } from '#/adapter/vxe-table';

import { deleteJob<PERSON>pi, getJobListApi, updateJob<PERSON>pi } from '@vben/api';
import { Page, useVbenModal } from '@vben/common-ui';
import { Plus } from '@vben/icons';

import { ElButton, ElMessage } from 'element-plus';

import { useVbenVxeGrid, vxeCheckboxChecked } from '#/adapter/vxe-table';
import { useHandleData } from '#/composables/useHandleData';

import { useColumns } from './data';
import Form from './modules/form.vue';

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
});

/**
 * 操作按钮点击事件
 * @param {OnActionClickParams<Permission.Job>} param
 */
function onActionClick({ code, row }: OnActionClickParams<Permission.Job>) {
  const actions: Record<string, () => void> = {
    edit: () => open(row),
    delete: () => onDelete(row),
  };

  actions[code]?.();
}

/**
 * 打开表单
 * @param row 岗位信息
 */
function open(row: Partial<Permission.Job> = {}) {
  formModalApi.setData(row).open();
}

/**
 * 删除岗位
 * @param row
 */
async function onDelete(row: Permission.Job) {
  await deleteJobApi(row.id);
  ElMessage.success('删除成功');
  refreshGrid();
}

/**
 * 切换状态
 * @param newStatus 新状态
 * @param row 岗位信息
 */
function onStatusChange(newStatus: number, row: Permission.Job) {
  return useHandleData(updateJobApi, { id: row.id, status: newStatus }, `切换状态`);
}

function onBatchDelete() {
  console.warn('onBatchDelete', gridApi.grid.getCheckboxRecords());
}

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: useColumns(onActionClick, onStatusChange),
    proxyConfig: {
      ajax: {
        query: async (_params) => {
          return await getJobListApi({ page: 1, pageSize: 100 });
        },
      },
    },
    showOverflow: false,
  } as VxeTableGridOptions,
});
</script>
<template>
  <Page auto-content-height>
    <FormModal @success="refreshGrid" />
    <Grid table-title="岗位列表">
      <template #toolbar-tools>
        <ElButton type="primary" @click="open">
          <Plus class="size-5" />
          新增岗位
        </ElButton>
        <ElButton type="danger" :disabled="!vxeCheckboxChecked(gridApi)" @click="onBatchDelete">
          <span class="icon-[mdi--trash-can-outline] size-5"></span>
          批量删除
        </ElButton>
      </template>
    </Grid>
  </Page>
</template>
