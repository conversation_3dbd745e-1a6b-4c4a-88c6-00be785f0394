<script lang="ts" setup>
import type { Permission } from '@vben/api';

import { computed, ref } from 'vue';

import { createJobApi, updateJobApi } from '@vben/api';
import { useVbenModal } from '@vben/common-ui';

import { ElMessage } from 'element-plus';

import { useVbenForm } from '#/adapter/form';

import { useSchema } from '../data';

const emit = defineEmits(['success']);
const formData = ref<Permission.Job>();
const getTitle = computed(() => {
  return formData.value?.id ? `编辑岗位` : `新增岗位`;
});

const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: useSchema(),
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (valid) {
      modalApi.lock();
      const data = await formApi.getValues();
      try {
        await (formData.value?.id ? updateJobApi({ id: formData.value.id, data }) : createJobApi(data));
        ElMessage.success(`${getTitle.value}成功`);
        modalApi.close();
        emit('success');
      } finally {
        modalApi.lock(false);
      }
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<Permission.Job>();
      if (data) {
        formData.value = data;
        formApi.setValues(formData.value);
      }
    }
  },
});
</script>

<template>
  <Modal :title="getTitle">
    <Form class="mx-4" />
  </Modal>
</template>
