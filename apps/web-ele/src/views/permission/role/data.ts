import type { Permission } from '@vben/api';

import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';

import { getDepartmentListApi, getRoleListApi } from '@vben/api';

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiTreeSelect',
      componentProps: {
        allowClear: true,
        api: getRoleListApi,
        class: 'w-full',
        labelField: 'role_name',
        valueField: 'id',
        childrenField: 'children',
        placeholder: '请选择上级角色',
      },
      fieldName: 'parent_id',
      label: '上级角色',
    },
    {
      component: 'Input',
      fieldName: 'role_name',
      label: '角色名称',
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'identify',
      label: '角色标识',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        type: 'textarea',
        rows: 4,
      },
      fieldName: 'description',
      formItemClass: 'items-start',
      label: '角色描述',
    },

    {
      component: 'RadioGroup',
      fieldName: 'status',
      label: '角色状态',
      defaultValue: 1,
      componentProps: {
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 2 },
        ],
        optionType: 'button',
        buttonStyle: 'solid',
      },
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'permissions',
      formItemClass: 'items-start',
      label: '角色权限',
      modelPropName: 'modelValue',
    },
    {
      component: 'Select',
      fieldName: 'data_range',
      label: '数据权限',
      componentProps: {
        allowClear: true,
        class: 'w-full',
        placeholder: '请选择数据权限',
        options: [
          { label: '全部数据', value: 1 },
          { label: '自定义数据', value: 2 },
          { label: '本人数据', value: 3 },
          { label: '本部门数据', value: 4 },
          { label: '本部门及以下数据', value: 5 },
        ],
      },
      rules: 'required',
    },
    {
      component: 'ApiTreeSelect',
      fieldName: 'departments',
      label: '自定义部门',
      componentProps: {
        allowClear: true,
        class: 'w-full',
        api: getDepartmentListApi,
        labelField: 'department_name',
        valueField: 'id',
        childrenField: 'children',
        placeholder: '请选择部门',
        multiple: true,
        treeCheckable: true,
        treeDefaultExpandAll: true,
      },
      dependencies: {
        show: (values) => values.data_range === 2,
        triggerFields: ['data_range'],
      },
    },
    {
      component: 'InputNumber',
      fieldName: 'sort',
      label: '排序',
      defaultValue: 100,
      componentProps: {
        min: 0,
        max: 9999,
        class: 'w-full',
        placeholder: '请输入排序值',
      },
    },
  ];
}

// 搜索
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'role_name',
      label: '角色名称',
    },
    {
      component: 'Input',
      fieldName: 'identify',
      label: '角色标识',
    },
  ];
}

export function useColumns<T = Permission.Role>(
  onActionClick: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<any>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'id',
      title: 'ID',
      width: 80,
    },
    {
      field: 'role_name',
      title: '角色名称',
      width: 300,
      treeNode: true,
      align: 'left',
    },

    {
      field: 'identify',
      title: '角色标识',
      width: 200,
    },
    {
      cellRender: {
        attrs: { beforeChange: onStatusChange },
        name: onStatusChange ? 'CellSwitch' : 'CellTag',
      },
      field: 'status',
      title: '状态',
      width: 100,
    },
    {
      field: 'description',
      minWidth: 100,
      title: '角色描述',
    },
    {
      field: 'created_at',
      title: '创建时间',
      width: 200,
      cellRender: {
        name: 'CellDatetime',
      },
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'role_name',
          nameTitle: '角色名称',
          onClick: onActionClick,
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 130,
    },
  ];
}
