<script lang="ts" setup>
import type { Permission } from '@vben/api';

import type { OnActionClickParams, VxeTableGridOptions } from '#/adapter/vxe-table';

import { deleteRoleApi, getRoleListApi, updateRoleApi } from '@vben/api';
import { Page, useVbenModal } from '@vben/common-ui';
import { Plus } from '@vben/icons';

import { ElButton } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { useHandleData } from '#/composables/useHandleData';

import { useColumns, useGridFormSchema } from './data';
import Form from './modules/form.vue';

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
  class: 'w-full lg:w-[600px]',
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: useGridFormSchema(),
    submitOnChange: true,
  },
  gridOptions: {
    columns: useColumns(onActionClick, onStatusChange),
    height: 'auto',
    keepSource: true,
    pagerConfig: {
      enabled: false,
    },
    proxyConfig: {
      ajax: {
        query: async ({ page: _page }, formValues) => {
          return await getRoleListApi({
            ...formValues,
          });
        },
      },
    },
  } as VxeTableGridOptions<Permission.Role>,
});

/**
 * 操作按钮点击事件
 * @param {OnActionClickParams<Permission.Role>} param
 */
function onActionClick({ code, row }: OnActionClickParams<Permission.Role>) {
  const actions: Record<string, () => void> = {
    edit: () => onOpen(row),
    delete: () => onDelete(row),
    view: () => onOpen({ ...row, __view: true }),
  };

  actions[code]?.();
}

/**
 * 打开表单
 * @param row 行数据
 */
async function onOpen(row: Partial<Permission.Role> = {}) {
  formModalApi.setData(row).open();
}

/**
 * 切换状态
 * @param newStatus 期望改变的状态值
 * @param row 行数据
 * @returns 返回false则中止改变，返回其他值（undefined、true）则允许改变
 */
async function onStatusChange(newStatus: number, row: Permission.Role) {
  return useHandleData(updateRoleApi, { id: row.id, status: newStatus }, '切换状态');
}

async function onDelete(row: Permission.Role) {
  await useHandleData(deleteRoleApi, row.id, '删除角色');
}

function onRefresh() {
  gridApi.query();
}
</script>
<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />
    <Grid table-title="角色列表">
      <template #toolbar-tools>
        <ElButton type="primary" @click="onOpen">
          <Plus class="size-5" />
          新增角色
        </ElButton>
      </template>
    </Grid>
  </Page>
</template>
