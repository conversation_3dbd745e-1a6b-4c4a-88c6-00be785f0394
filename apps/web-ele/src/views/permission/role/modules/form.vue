<script lang="ts" setup>
import type { Permission } from '@vben/api';
import type { Recordable } from '@vben/types';

import { computed, ref } from 'vue';

import { createRoleApi, getPermissionListApi, getRoleDetailApi, updateRoleApi } from '@vben/api';
import { useVbenModal, VbenTree } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import { useVbenForm } from '#/adapter/form';

import { useFormSchema } from '../data';

const emits = defineEmits(['success']);

const formData = ref<Permission.Role>();

const [Form, formApi] = useVbenForm({
  schema: useFormSchema(),
  showDefaultActions: false,
  commonConfig: {
    labelClass: 'w-[80px]',
  },
});

const permissions = ref<any[]>([]);
const loadingPermissions = ref(false);

const id = ref();
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const values = await formApi.getValues();
    modalApi.lock();
    (id.value ? updateRoleApi({ id: id.value, ...values }) : createRoleApi(values))
      .then(() => {
        emits('success');
        modalApi.close();
      })
      .catch(() => {
        modalApi.unlock();
      });
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<Permission.Role>();
      formApi.resetForm();
      if (data) {
        if (data.parent_id === 0) {
          data.parent_id = undefined;
        }
        formData.value = data;
        id.value = data.id;

        if (data.id) {
          const res = await getRoleDetailApi(data.id);
          data.permissions = res.permissions;
          data.departments = res.departments;
        }
        await formApi.setValues(data);
      } else {
        id.value = undefined;
      }

      if (permissions.value.length === 0) {
        loadPermissions();
      }
    }
  },
});

async function loadPermissions() {
  loadingPermissions.value = true;
  try {
    const res = await getPermissionListApi();
    permissions.value = res as unknown as any[];
  } finally {
    loadingPermissions.value = false;
  }
}

const getModalTitle = computed(() => {
  return formData.value?.id ? '编辑角色' : '创建角色';
});

function getNodeClass(node: Recordable<any>) {
  const classes: string[] = [];
  if (node.value?.type === 'button') {
    classes.push('inline-flex');
    if (node.index % 3 >= 1) {
      classes.push('!pl-0');
    }
  }

  return classes.join(' ');
}
</script>
<template>
  <Modal :title="getModalTitle">
    <Form>
      <template #permissions="slotProps">
        <div v-loading="loadingPermissions" element-loading-text="加载中..." class="w-full">
          <VbenTree
            :tree-data="permissions"
            multiple
            bordered
            :get-node-class="getNodeClass"
            v-bind="slotProps"
            value-field="id"
            label-field="meta.title"
            icon-field="meta.icon"
            id-field="id"
          >
            <template #node="{ value }">
              <IconifyIcon v-if="value.meta.icon" :icon="value.meta.icon" />
              {{ value.meta.title }}
            </template>
          </VbenTree>
        </div>
      </template>
    </Form>
  </Modal>
</template>
<style lang="css" scoped>
:deep(.ant-tree-title) {
  .tree-actions {
    display: none;
    margin-left: 20px;
  }
}

:deep(.ant-tree-title:hover) {
  .tree-actions {
    display: flex;
    flex: auto;
    justify-content: flex-end;
    margin-left: 20px;
  }
}
</style>
