import type { Permission } from '@vben/api';

import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';

export function getMenuTypeOptions() {
  return [
    {
      color: 'processing',
      label: '目录',
      value: 'catalog',
    },
    { color: 'default', label: '菜单', value: 'menu' },
    { color: 'error', label: '按钮', value: 'action' },
    {
      color: 'success',
      label: '内嵌页',
      value: 'embedded',
    },
    { color: 'warning', label: '外链', value: 'link' },
  ];
}

export function useColumns(
  onActionClick: OnActionClickFn<Permission.Permission>,
): VxeTableGridOptions<Permission.Permission>['columns'] {
  return [
    {
      align: 'left',
      field: 'meta.title',
      fixed: 'left',
      slots: { default: 'title' },
      title: '菜单名称',
      treeNode: true,
      width: 250,
    },
    {
      align: 'center',
      cellRender: { name: 'CellTag', options: getMenuTypeOptions() },
      field: 'type',
      title: '类型',
      width: 100,
    },
    {
      align: 'left',
      field: 'auth_code',
      title: '权限标识',
      width: 200,
    },
    {
      align: 'left',
      field: 'path',
      title: '路由路径',
      width: 200,
    },
    {
      align: 'center',
      field: 'sort',
      title: '排序',
      width: 100,
    },
    {
      align: 'left',
      field: 'component',
      formatter: ({ row }) => {
        switch (row.type) {
          case 'catalog':
          case 'menu': {
            return row.component ?? '';
          }
          case 'embedded': {
            return row.meta?.iframeSrc ?? '';
          }
          case 'link': {
            return row.meta?.link ?? '';
          }
        }
        return '';
      },
      minWidth: 200,
      title: '组件/内嵌页/外链',
    },
    {
      cellRender: { name: 'CellTag' },
      field: 'status',
      title: '状态',
      width: 100,
    },

    {
      align: 'right',
      cellRender: {
        attrs: {
          nameField: 'name',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'append',
            buttonText: '新增下级',
          },
          'edit', // 默认的编辑按钮
          'delete', // 默认的删除按钮
        ],
      },
      field: 'operation',
      fixed: 'right',
      headerAlign: 'center',
      showOverflow: false,
      title: '操作',
      width: 200,
    },
  ];
}
