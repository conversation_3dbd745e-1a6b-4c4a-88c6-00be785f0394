<script lang="ts" setup>
import type { Permission } from '@vben/api';
import type { Recordable } from '@vben/types';

import type { VbenFormSchema } from '#/adapter/form';

import { computed, h, ref } from 'vue';

import { createPermissionApi, getPermissionList<PERSON>pi, updatePermissionApi } from '@vben/api';
import { useVbenDrawer } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';
import { getPopupContainer } from '@vben/utils';

import { breakpointsTailwind, useBreakpoints } from '@vueuse/core';

import { useVbenForm, z } from '#/adapter/form';
import { componentKeys } from '#/router/routes';

import { getMenuTypeOptions } from '../data';

const emit = defineEmits<{
  success: [];
}>();

const BadgeVariants = ['default', 'destructive', 'primary', 'success', 'warning'];

const formData = ref<Permission.Permission>();
const titleSuffix = ref<string>();
const schema: VbenFormSchema[] = [
  {
    component: 'RadioGroup',
    componentProps: {
      buttonStyle: 'solid',
      options: getMenuTypeOptions(),
      optionType: 'button',
    },
    defaultValue: 'menu',
    fieldName: 'type',
    formItemClass: 'col-span-2 md:col-span-2',
    label: '类型',
  },
  {
    component: 'Input',
    fieldName: 'title',
    label: '菜单名称',
    rules: z.string().min(2, '菜单名称最少2个字符').max(30, '菜单名称最多30个字符'),
  },
  {
    component: 'ApiTreeSelect',
    componentProps: {
      api: getPermissionListApi,
      class: 'w-full',
      filterTreeNode(input: string, node: Recordable<any>) {
        if (!input || input.length === 0) {
          return true;
        }
        const title: string = node.meta?.title ?? '';
        if (!title) return false;
        return title.includes(input);
      },
      getPopupContainer,
      labelField: 'meta.title',
      showSearch: true,
      treeDefaultExpandAll: true,
      valueField: 'id',
      childrenField: 'children',
    },
    fieldName: 'parent_id',
    label: '上级菜单',
    renderComponentContent() {
      return {
        title({ label, meta }: { label: string; meta: Recordable<any> }) {
          const coms = [];
          if (!label) return '';
          if (meta?.icon) {
            coms.push(h(IconifyIcon, { class: 'size-4', icon: meta.icon }));
          }
          coms.push(h('span', { class: '' }, label || ''));
          return h('div', { class: 'flex items-center gap-1' }, coms);
        },
      };
    },
  },
  {
    component: 'Input',
    fieldName: 'meta.title',
    label: '显示名称',
    rules: 'required',
  },
  {
    component: 'Input',
    dependencies: {
      show: (values) => {
        return ['catalog', 'embedded', 'menu'].includes(values.type);
      },
      triggerFields: ['type'],
    },
    fieldName: 'path',
    label: '路由路径',
    rules: z
      .string()
      .min(2, '路由路径最少2个字符')
      .max(100, '路由路径最多100个字符')
      .refine((value: string) => {
        return value.startsWith('/');
      }, '路由路径必须以/开头'),
  },
  {
    component: 'Input',
    dependencies: {
      show: (values) => {
        return ['embedded', 'menu'].includes(values.type);
      },
      triggerFields: ['type'],
    },
    fieldName: 'active_path',
    help: '用于高亮的菜单路径',
    label: '高亮路径',
    rules: z
      .string()
      .min(2, '高亮路径最少2个字符')
      .max(100, '高亮路径最多100个字符')
      .refine((value: string) => {
        return value.startsWith('/');
      }, '高亮路径必须以/开头')
      .optional(),
  },
  {
    component: 'IconPicker',
    componentProps: {
      prefix: 'carbon',
    },
    dependencies: {
      show: (values) => {
        return ['catalog', 'embedded', 'link', 'menu'].includes(values.type);
      },
      triggerFields: ['type'],
    },
    fieldName: 'meta.icon',
    label: '图标',
  },
  {
    component: 'IconPicker',
    componentProps: {
      prefix: 'carbon',
    },
    dependencies: {
      show: (values) => {
        return ['catalog', 'embedded', 'menu'].includes(values.type);
      },
      triggerFields: ['type'],
    },
    fieldName: 'meta.activeIcon',
    label: '激活图标',
  },
  {
    component: 'AutoComplete',
    componentProps: {
      clearable: true,
      class: 'w-full',
      fetchSuggestions(queryString: string, cb: (suggestions: any[]) => void) {
        const suggestions = componentKeys
          .filter((key) => key.toLowerCase().includes(queryString.toLowerCase()))
          .map((key) => ({ value: key }));
        cb(suggestions);
      },
      placeholder: '请选择页面组件',
    },
    dependencies: {
      rules: (values) => {
        return values.type === 'menu' ? 'required' : null;
      },
      show: (values) => {
        return values.type === 'menu';
      },
      triggerFields: ['type'],
    },
    fieldName: 'component',
    label: '页面组件',
  },
  {
    component: 'Input',
    dependencies: {
      show: (values) => {
        return ['embedded', 'link'].includes(values.type);
      },
      triggerFields: ['type'],
    },
    fieldName: 'link_src',
    label: '链接地址',
    rules: z.string().url('请输入有效的链接'),
  },
  {
    component: 'Input',
    dependencies: {
      rules: (values) => {
        return values.type === 'action' ? 'required' : null;
      },
      show: (values) => {
        return ['action', 'catalog', 'embedded', 'menu'].includes(values.type);
      },
      triggerFields: ['type'],
    },
    fieldName: 'auth_code',
    label: '权限标识',
  },

  {
    component: 'Select',
    componentProps: {
      allowClear: true,
      class: 'w-full',
      options: [
        { label: '圆点', value: 'dot' },
        { label: '文字', value: 'normal' },
      ],
    },
    dependencies: {
      show: (values) => {
        return values.type !== 'action';
      },
      triggerFields: ['type'],
    },
    fieldName: 'meta.badgeType',
    label: '徽标类型',
  },
  {
    component: 'Input',
    componentProps: (values) => {
      return {
        allowClear: true,
        class: 'w-full',
        disabled: values.meta?.badgeType !== 'normal',
      };
    },
    dependencies: {
      show: (values) => {
        return values.type !== 'action';
      },
      triggerFields: ['type'],
    },
    fieldName: 'meta.badge',
    label: '徽标内容',
  },
  {
    component: 'Select',
    componentProps: {
      allowClear: true,
      class: 'w-full',
      options: BadgeVariants.map((v) => ({
        label: v,
        value: v,
      })),
    },
    dependencies: {
      show: (values) => {
        return values.type !== 'action';
      },
      triggerFields: ['type'],
    },
    fieldName: 'meta.badgeVariants',
    label: '徽标样式',
  },
  {
    component: 'Input',
    fieldName: 'module',
    label: '模块',
    componentProps: {
      allowClear: true,
      class: 'w-full',
    },
    rules: z.string().max(20, '模块名称最多20个字符').optional(),
  },
  {
    component: 'InputNumber',
    fieldName: 'sort',
    label: '排序',
    defaultValue: 100,
    componentProps: {
      min: 0,
      max: 9999,
      class: 'w-full',
    },
    rules: z.number().int('必须为整数').min(0, '最小为0').max(9999, '最大为9999').optional(),
  },
  {
    component: 'RadioGroup',
    componentProps: {
      buttonStyle: 'solid',
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 2 },
      ],
      optionType: 'button',
    },
    defaultValue: 1,
    fieldName: 'status',
    label: '状态',
  },
  {
    component: 'Divider',
    dependencies: {
      show: (values) => {
        return !['action', 'link'].includes(values.type);
      },
      triggerFields: ['type'],
    },
    fieldName: 'divider1',
    formItemClass: 'col-span-2 md:col-span-2 pb-0',
    hideLabel: true,
    renderComponentContent() {
      return {
        default: () => '高级设置',
      };
    },
  },
  {
    component: 'Checkbox',
    dependencies: {
      show: (values) => {
        return ['menu'].includes(values.type);
      },
      triggerFields: ['type'],
    },
    fieldName: 'meta.keepAlive',
    renderComponentContent() {
      return {
        default: () => '缓存标签(KeepAlive)',
      };
    },
  },
  {
    component: 'Checkbox',
    dependencies: {
      show: (values) => {
        return ['embedded', 'menu'].includes(values.type);
      },
      triggerFields: ['type'],
    },
    fieldName: 'meta.affixTab',
    renderComponentContent() {
      return {
        default: () => '固定在标签',
      };
    },
  },
  {
    component: 'Checkbox',
    dependencies: {
      show: (values) => {
        return !['action'].includes(values.type);
      },
      triggerFields: ['type'],
    },
    fieldName: 'meta.hideInMenu',
    renderComponentContent() {
      return {
        default: () => '隐藏菜单',
      };
    },
  },
  {
    component: 'Checkbox',
    dependencies: {
      show: (values) => {
        return ['catalog', 'menu'].includes(values.type);
      },
      triggerFields: ['type'],
    },
    fieldName: 'meta.hideChildrenInMenu',
    renderComponentContent() {
      return {
        default: () => '隐藏子菜单',
      };
    },
  },
  {
    component: 'Checkbox',
    dependencies: {
      show: (values) => {
        return !['action', 'link'].includes(values.type);
      },
      triggerFields: ['type'],
    },
    fieldName: 'meta.hideInBreadcrumb',
    renderComponentContent() {
      return {
        default: () => '面包屑中隐藏',
      };
    },
  },
  {
    component: 'Checkbox',
    dependencies: {
      show: (values) => {
        return !['action', 'link'].includes(values.type);
      },
      triggerFields: ['type'],
    },
    fieldName: 'meta.hideInTab',
    renderComponentContent() {
      return {
        default: () => '标签栏中隐藏',
      };
    },
  },
];

const breakpoints = useBreakpoints(breakpointsTailwind);
const isHorizontal = computed(() => breakpoints.greaterOrEqual('md').value);

const [Form, formApi] = useVbenForm({
  commonConfig: {
    colon: true,
    formItemClass: 'col-span-2 md:col-span-1',
  },
  schema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 gap-x-4',
});

const [Drawer, drawerApi] = useVbenDrawer({
  onConfirm: onSubmit,
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<Permission.Permission>();
      if (String(data?.type) === 'link') {
        data.linkSrc = data.meta?.link;
      } else if (String(data?.type) === 'embedded') {
        data.linkSrc = data.meta?.iframeSrc;
      }
      if (data) {
        formData.value = data;
        formApi.setValues(formData.value);
        titleSuffix.value = formData.value.meta?.title || '';
      } else {
        formApi.resetForm();
        titleSuffix.value = '';
      }
    }
  },
});

async function onSubmit() {
  const { valid } = await formApi.validate();
  if (valid) {
    drawerApi.lock();
    const data = await formApi.getValues<Omit<Permission.Permission, 'children' | 'id'>>();
    if (String(data.type) === 'link') {
      data.meta = { ...data.meta, link: data.linkSrc };
    } else if (String(data.type) === 'embedded') {
      data.meta = { ...data.meta, iframeSrc: data.linkSrc };
    }
    delete data.linkSrc;
    console.warn(data);
    console.warn(formData.value);
    try {
      await (formData.value?.id ? updatePermissionApi(formData.value.id, data) : createPermissionApi(data));
      drawerApi.close();
      emit('success');
    } finally {
      drawerApi.unlock();
    }
  }
}
const getDrawerTitle = computed(() => (formData.value?.id ? '编辑菜单' : '新建菜单'));
</script>
<template>
  <Drawer class="w-full max-w-[800px]" :title="getDrawerTitle">
    <Form class="mx-4" :layout="isHorizontal ? 'horizontal' : 'vertical'" />
  </Drawer>
</template>
