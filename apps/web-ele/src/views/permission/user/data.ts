import type { Permission } from '@vben/api';

import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';

import { getDepartmentListApi, getJobListApi, getRoleListApi } from '@vben/api';

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'username',
      label: '用户名',
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'mobile',
      label: '手机',
    },
    {
      component: 'Input',
      fieldName: 'fullname',
      label: '真实姓名',
    },
    {
      component: 'RadioGroup',
      fieldName: 'gender',
      label: '性别',
      defaultValue: 1,
      componentProps: {
        class: 'w-full',
        options: [
          { label: '男', value: 1 },
          { label: '女', value: 2 },
        ],
      },
    },
    {
      component: 'Input',
      fieldName: 'email',
      label: '邮箱',
    },
    {
      component: 'Input',
      fieldName: 'password',
      label: '用户密码',
      componentProps: {
        type: 'password',
        autocomplete: 'new-password',
      },
    },
    {
      component: 'DatePicker',
      fieldName: 'birthday',
      label: '生日',
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
        allowClear: true,
        placeholder: '请选择生日',
        class: 'w-full',
      },
    },
    {
      component: 'DepartmentSelector',
      fieldName: 'departmentInfo',
      formItemClass: 'col-span-1',
      label: '所属部门',
      componentProps: {
        requestApi: getDepartmentListApi,
        id: 'id',
        label: 'department_name',
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'jobs',
      label: '所属岗位',
      componentProps: {
        multiple: true,
        api: getJobListApi,
        resultField: 'items',
        valueField: 'id',
        labelField: 'job_name',
        mode: 'tags',
        placeholder: '请选择所属岗位',
        class: 'w-full',
      },
    },
    {
      component: 'ApiTreeSelect',
      fieldName: 'roles',
      label: '所属角色',
      componentProps: {
        api: getRoleListApi,
        resultField: 'items',
        valueField: 'id',
        labelField: 'role_name',
        childrenField: 'children',
        multiple: true,
        showSearch: true,
        treeDefaultExpandAll: true,
        maxTagCount: 2,
        placeholder: '请选择所属角色',
        allowClear: true,
        class: 'w-full',
      },
    },
    {
      component: 'Switch',
      fieldName: 'status',
      label: '用户状态',
      defaultValue: 1,
      componentProps: {
        checkedValue: 1,
        unCheckedValue: 2,
        checkedChildren: '正常',
        unCheckedChildren: '禁用',
      },
    },
    {
      component: 'UploadImg',
      formItemClass: 'col-span-2',
      modelPropName: 'imageUrl',
      componentProps: {
        useCos: true,
      },
      fieldName: 'avatar',
      label: '头像',
    },
  ];
}

// 搜索
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'fullname',
      label: '真实姓名',
    },
    {
      component: 'Input',
      fieldName: 'mobile',
      label: '手机号',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '用户状态',
      componentProps: {
        options: [
          { label: '正常', value: 1 },
          { label: '禁用', value: 2 },
        ],
      },
    },
  ];
}
export function useColumns<T = Permission.User>(
  onActionClick: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<any>,
): VxeTableGridOptions['columns'] {
  return [
    { type: 'checkbox', width: 50, fixed: 'left' },
    {
      field: 'id',
      title: 'ID',
      width: 80,
    },
    {
      field: 'avatar',
      title: '头像',
      width: 80,
      slots: { default: 'avatar' },
    },
    {
      field: 'username',
      title: '用户名',
      width: 200,
      slots: { default: 'username' },
    },
    {
      field: 'fullname',
      title: '姓名',
      width: 200,
    },
    {
      field: 'email',
      title: '邮箱',
    },
    {
      field: 'description',
      minWidth: 100,
      title: '备注',
    },
    {
      field: 'created_at',
      title: '创建时间',
      cellRender: {
        name: 'CellDatetime',
      },
    },
    // 状态
    {
      cellRender: {
        attrs: { beforeChange: onStatusChange },
        name: onStatusChange ? 'CellSwitch' : 'CellTag',
      },
      field: 'status',
      title: '状态',
      width: 100,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'username',
          nameTitle: '用户',
          onClick: onActionClick,
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 150,
    },
  ];
}
