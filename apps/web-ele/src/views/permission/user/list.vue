<script lang="ts" setup>
import type { Permission } from '@vben/api';

import type { OnActionClickParams, VxeTableGridOptions } from '#/adapter/vxe-table';

import { reactive, ref } from 'vue';

import { deleteUserApi, getDepartment<PERSON>ist<PERSON>pi, getUserListApi, updateUser<PERSON>pi } from '@vben/api';
import { ColPage, useVbenModal } from '@vben/common-ui';
import { IconifyIcon, Plus } from '@vben/icons';

import { ElAvatar, ElButton, ElMessage, ElTooltip } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import TreeFilter from '#/components/TreeFilter/index.vue';
import { useHandleData } from '#/composables/useHandleData';

import { useColumns, useGridFormSchema } from './data';
import Form from './modules/form.vue';

const props = reactive({
  leftCollapsedWidth: 5,
  // 是否可折叠
  leftCollapsible: false,
  leftMaxWidth: 50,
  leftMinWidth: 10,
  leftWidth: 20,
  resizable: true,
  rightWidth: 80,
  // 是否显示拖动分隔线
  splitLine: true,
  // 是否显示拖动手柄
  splitHandle: true,
});

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
  class: 'w-full lg:w-[800px]',
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    collapsed: true,
    schema: useGridFormSchema(),
    submitOnChange: true,
    wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  },
  gridOptions: {
    columns: useColumns(onActionClick, onStatusChange),
    showOverflow: false,
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const params = { page: page.currentPage, limit: page.pageSize, ...formValues };
          if (departmentId.value) {
            params.department_id = departmentId.value; // 仅在有值时传递部门ID
          }
          return await getUserListApi(params);
        },
      },
    },
  } as VxeTableGridOptions<Permission.User>,
});

function onActionClick(e: OnActionClickParams<Permission.User>) {
  const actions: Record<string, () => void> = {
    edit: () => onOpen(e.row),
    delete: () => onDelete(e.row),
    view: () => onView(e.row),
  };

  actions[e.code]?.();
}

function onOpen(row: Partial<Permission.User> = {}) {
  formModalApi.setData(row).open();
}

/**
 * 状态开关即将改变
 * @param newStatus 期望改变的状态值
 * @param row 行数据
 * @returns 返回false则中止改变，返回其他值（undefined、true）则允许改变
 */
async function onStatusChange(newStatus: number, row: Permission.User) {
  return useHandleData(updateUserApi, { id: row.id, status: newStatus }, `切换状态`);
}

function onView(row: Permission.User) {
  formModalApi.setData(row).setState({ isView: true }).open();
}

async function onDelete(row: Permission.User) {
  await deleteUserApi(row.id);
  ElMessage.success('删除成功');
  onRefresh();
}

function onRefresh() {
  gridApi.query();
}

const departmentId = ref<string>('');

function onTreeFilterChange(value: string) {
  departmentId.value = value;

  gridApi.query();
}

function onBatchDelete() {
  const ids = gridApi.grid.getCheckboxRecords().map((item) => item.id);
  if (ids.length === 0) {
    ElMessage.warning('请至少选择一个用户');
    return;
  }
  console.warn(ids);
  ElMessage.warning(`批量删除${ids.length}个用户`);
}
</script>
<template>
  <ColPage auto-content-height v-bind="props" title="用户管理">
    <template #title>
      <span class="mr-2 text-2xl font-bold">用户管理</span>
    </template>
    <template #left="{ isCollapsed, expand }">
      <div v-if="isCollapsed" @click="expand">
        <ElTooltip title="点击展开左侧">
          <ElButton shape="circle" type="primary">
            <template #icon>
              <IconifyIcon class="text-2xl" icon="bi:arrow-right" />
            </template>
          </ElButton>
        </ElTooltip>
      </div>
      <div
        v-else
        :style="{ minWidth: '200px' }"
        class="border-border bg-card mr-2 h-full rounded-[var(--radius)] border border-gray-100 p-2"
      >
        <TreeFilter
          title="部门"
          :request-api="getDepartmentListApi"
          id="id"
          label="department_name"
          @change="onTreeFilterChange"
        />
      </div>
    </template>
    <div class="ml-2 h-full">
      <FormModal @success="onRefresh" />
      <Grid table-title="用户列表">
        <template #table-title>
          <ElButton type="primary" @click="onOpen" class="flex items-center">
            <Plus class="size-4" />
            新增用户
          </ElButton>
          <ElButton danger type="primary" class="ml-2 flex items-center" @click="onBatchDelete">
            <template #icon>
              <IconifyIcon icon="bi:trash" />
            </template>
            批量删除
          </ElButton>
        </template>

        <template #avatar="{ row }"> <ElAvatar :src="row.avatar" :size="30" /> </template>
        <template #username="{ row }">
          <a @click="onView(row)" class="font-bold text-blue-500">
            {{ row.username }}
          </a>
        </template>
      </Grid>
    </div>
  </ColPage>
</template>
