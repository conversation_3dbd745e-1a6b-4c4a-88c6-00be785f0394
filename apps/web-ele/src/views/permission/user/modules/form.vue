<script lang="ts" setup>
import type { Permission } from '@vben/api';

import { computed, ref } from 'vue';

import { createUserApi, updateUserApi } from '@vben/api';
import { useVbenModal } from '@vben/common-ui';

import { ElButton } from 'element-plus';

import { useVbenForm } from '#/adapter/form';

import { useFormSchema } from '../data';

const emits = defineEmits<{
  success: [];
}>();

const userId = ref<number>();
const formData = ref<Permission.User>();

const [Form, formApi] = useVbenForm({
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1 lg:grid-cols-2',
});

const [Modal, modalApi] = useVbenModal({
  footer: false,
  async onConfirm() {
    await handleFormSubmit();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      handleModalOpen();
    }
  },
});

// 计算属性
const isViewMode = computed(() => !!modalApi.useStore().value.isView);

const modalTitle = computed(() => {
  if (isViewMode.value) return '查看用户';
  return userId.value ? '编辑用户' : '新增用户';
});

// 业务逻辑函数
async function handleFormSubmit(): Promise<void> {
  const { valid } = await formApi.validate();
  if (!valid) return;

  const values = await formApi.getValues();
  modalApi.lock();

  try {
    const apiCall = userId.value ? updateUserApi({ id: userId.value, ...values }) : createUserApi(values);

    await apiCall;
    emits('success');
    modalApi.close();
  } catch (error) {
    modalApi.unlock();
    // 这里可以添加具体的错误处理逻辑
    console.error('保存用户信息失败:', error);
  }
}

function handleModalOpen(): void {
  // 设置查看模式的表单状态
  if (isViewMode.value) {
    formApi.setState({ commonConfig: { disabled: true } });
  }

  const userData = modalApi.getData<Permission.User>();
  formApi.resetForm();

  if (userData) {
    initializeFormWithUserData(userData);
  } else {
    resetFormToDefault();
  }
}

function initializeFormWithUserData(userData: Permission.User): void {
  formData.value = userData;
  userId.value = userData.id;

  // 处理并设置表单数据
  const processedData = { ...userData };

  // 处理岗位数据：将对象数组转换为ID数组
  if (processedData.jobs && Array.isArray(processedData.jobs)) {
    processedData.jobs = processedData.jobs.map((job: Permission.Job) => job.id);
  }

  formApi.setValues(processedData);
}

function resetFormToDefault(): void {
  userId.value = undefined;
  formData.value = undefined;
}
</script>

<template>
  <Modal :title="modalTitle">
    <Form />
    <template #footer v-if="!isViewMode">
      <ElButton type="primary" @click="modalApi.onConfirm()"> 保存 </ElButton>
      <ElButton @click="modalApi.close()"> 取消 </ElButton>
    </template>
  </Modal>
</template>

<style lang="css" scoped></style>
