import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn } from '#/adapter/vxe-table';

/**
 * 附件类型
 */
export interface Attachment {
  id: number;
  filename: string;
  path: string;
  url: string;
  mime_type: string;
  file_ext: string;
  file_size: number;
  file_size_text: string;
  file_type_icon: string;
  driver: string;
  created_at: string;
  updated_at: string;
}

/**
 * 获取查看表单的字段配置
 */
export function useViewSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'filename',
      label: '文件名',
      componentProps: {
        readonly: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'mime_type',
      label: 'MIME类型',
      componentProps: {
        readonly: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'file_ext',
      label: '文件扩展名',
      componentProps: {
        readonly: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'file_size_text',
      label: '文件大小',
      componentProps: {
        readonly: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'driver',
      label: '存储驱动',
      componentProps: {
        readonly: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'path',
      label: '文件路径',
      componentProps: {
        readonly: true,
        type: 'textarea',
        rows: 2,
      },
    },
    {
      component: 'Input',
      fieldName: 'url',
      label: '访问地址',
      componentProps: {
        readonly: true,
        type: 'textarea',
        rows: 2,
      },
    },
    {
      component: 'Input',
      fieldName: 'created_at',
      label: '创建时间',
      componentProps: {
        readonly: true,
      },
    },
  ];
}

/**
 * 获取表格列配置
 */
export function useColumns<T = Attachment>(onActionClick?: OnActionClickFn<T>): VxeTableGridOptions['columns'] {
  return [
    { type: 'checkbox', width: 60 },
    {
      field: 'id',
      title: 'ID',
      width: 80,
    },
    {
      field: 'filename',
      title: '文件名',
      showOverflow: 'tooltip',
    },
    {
      field: 'url',
      title: '预览',
      width: 100,
      align: 'center',
      cellRender: {
        name: 'CellImage',
        attrs: {
          style: 'width: 30px;height: 30px;',
          fit: 'cover',
        },
      },
    },
    {
      field: 'file_ext',
      title: '文件后缀',
      width: 100,
      align: 'center',
      slots: {
        default: 'file_ext',
      },
      formatter: ({ cellValue }) => {
        return getFileIconByExt(cellValue);
      },
    },
    {
      field: 'path',
      title: '文件路径',
      width: 200,
      showOverflow: 'tooltip',
    },
    {
      field: 'file_size',
      title: '文件大小',
      width: 120,
      align: 'center',
      formatter: ({ cellValue }) => {
        if (!cellValue) return '-';
        return formatFileSize(cellValue);
      },
    },
    {
      field: 'mime_type',
      title: 'MIME类型',
      width: 150,
      showOverflow: 'tooltip',
    },
    {
      field: 'driver',
      title: '存储驱动',
      width: 100,
      align: 'center',
      formatter: ({ cellValue }) => {
        const driverMap: Record<string, string> = {
          local: '本地存储',
          oss: '阿里云OSS',
          qcloud: '腾讯云COS',
          qiniu: '七牛云',
        };
        return driverMap[cellValue] || cellValue;
      },
    },
    {
      field: 'created_at',
      title: '上传时间',
      width: 180,
    },

    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'filename',
          nameTitle: '附件',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: ['view', 'delete'],
      },
      field: 'operation',
      fixed: 'right',
      headerAlign: 'center',
      showOverflow: false,
      title: '操作',
      width: 200,
    },
  ];
}

/**
 * 格式化文件大小
 */
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
}

/**
 * 文件类型图标映射
 */
export const fileTypeIcons: Record<string, string> = {
  // 通用类型
  image: 'icon-[mdi--file-image-outline]',
  pdf: 'icon-[mdi--file-pdf-box]',
  doc: 'icon-[mdi--file-word-outline]',
  docx: 'icon-[mdi--file-word-outline]',
  xls: 'icon-[mdi--file-excel-outline]',
  xlsx: 'icon-[mdi--file-excel-outline]',
  ppt: 'icon-[mdi--file-powerpoint-outline]',
  pptx: 'icon-[mdi--file-powerpoint-outline]',
  zip: 'icon-[mdi--folder-zip-outline]',
  rar: 'icon-[mdi--folder-zip-outline]',
  '7z': 'icon-[mdi--folder-zip-outline]',
  tar: 'icon-[mdi--folder-zip-outline]',
  gz: 'icon-[mdi--folder-zip-outline]',
  mp4: 'icon-[mdi--file-video-outline]',
  avi: 'icon-[mdi--file-video-outline]',
  mov: 'icon-[mdi--file-video-outline]',
  mp3: 'icon-[mdi--file-music-outline]',
  wav: 'icon-[mdi--file-music-outline]',
  txt: 'icon-[mdi--file-outline]',
  csv: 'icon-[mdi--file-outline]',
  jpg: 'icon-[mdi--file-image-outline]',
  jpeg: 'icon-[mdi--file-image-outline]',
  png: 'icon-[mdi--file-image-outline]',
  gif: 'icon-[mdi--file-image-outline]',
  bmp: 'icon-[mdi--file-image-outline]',
  svg: 'icon-[mdi--file-image-outline]',
  // 兼容旧类型
  'file-pdf': 'icon-[mdi--file-pdf-box]',
  'file-word': 'icon-[mdi--file-word-outline]',
  'file-excel': 'icon-[mdi--file-excel-outline]',
  'file-powerpoint': 'icon-[mdi--file-powerpoint-outline]',
  'file-archive': 'icon-[mdi--file-zip-outline]',
  'file-video': 'icon-[mdi--file-video-outline]',
  'file-audio': 'icon-[mdi--file-music-outline]',
  file: 'icon-[mdi--file-outline]',
};

/**
 * 根据文件扩展名获取图标
 */
export function getFileIconByExt(fileExt: string): string {
  if (!fileExt) return fileTypeIcons.file || 'icon-[mdi--file-outline]';
  const ext = fileExt.toLowerCase();
  return fileTypeIcons[ext] || fileTypeIcons.file || 'icon-[mdi--file-outline]';
}

/**
 * 判断是否为图片文件
 */
export function isImageFile(mimeType: string): boolean {
  return mimeType.startsWith('image/');
}
