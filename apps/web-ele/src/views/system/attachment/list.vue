<script lang="ts" setup>
import type { Attachment } from './data';

import type { OnActionClickParams, VxeTableGridOptions } from '#/adapter/vxe-table';

import { batchDeleteAttachmentApi, deleteAttachmentApi, getAttachmentListApi } from '@vben/api';
import { Page, useVbenModal } from '@vben/common-ui';
import { Trash2 } from '@vben/icons';

import { ElButton, ElMessage } from 'element-plus';

import { useVbenVxeGrid, vxeCheckboxChecked } from '#/adapter/vxe-table';
import { useHandleData } from '#/composables/useHandleData';

import { getFileIconByExt, useColumns } from './data';
import ViewForm from './modules/form.vue';

const [ViewModal, viewModalApi] = useVbenModal({
  connectedComponent: ViewForm,
  destroyOnClose: true,
});

/**
 * 操作按钮点击事件
 */
function onActionClick({ code, row }: OnActionClickParams<Attachment>) {
  const actions: Record<string, () => void> = {
    view: () => openView(row),
    delete: () => onDelete(row),
  };

  actions[code]?.();
}

/**
 * 打开查看表单
 */
function openView(row: Attachment) {
  viewModalApi.setData(row).open();
}

/**
 * 删除附件
 */
async function onDelete(row: Attachment) {
  useHandleData(deleteAttachmentApi, row.id, '删除附件');
}

/**
 * 批量删除
 */
async function onBatchDelete() {
  const records = gridApi.grid.getCheckboxRecords() as Attachment[];
  if (records.length === 0) {
    ElMessage.warning('请选择要删除的附件');
    return;
  }

  useHandleData(
    batchDeleteAttachmentApi,
    records.map((record) => record.id),
    '批量删除附件',
  );
}

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    collapsed: true,
    submitOnChange: true,
    schema: [
      {
        label: '文件名',
        fieldName: 'filename',
        component: 'Input',
      },
      {
        label: '文件类型',
        fieldName: 'file_ext',
        component: 'Select',
        componentProps: {
          options: [
            { label: 'PNG', value: 'png' },
            { label: 'JPG', value: 'jpg' },
            { label: 'JPEG', value: 'jpeg' },
            { label: 'GIF', value: 'gif' },
            { label: 'BMP', value: 'bmp' },
            { label: 'SVG', value: 'svg' },
            { label: 'WEBP', value: 'webp' },
            { label: '其他', value: 'other' },
          ],
          placeholder: '请选择文件类型',
          clearable: true,
        },
      },
    ],
  },
  gridOptions: {
    columns: useColumns(onActionClick),
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          return await getAttachmentListApi({
            page: page.currentPage,
            limit: page.pageSize,
            ...formValues,
          });
        },
      },
    },
    showOverflow: false,
  } as VxeTableGridOptions,
});
</script>

<template>
  <Page auto-content-height>
    <ViewModal />

    <Grid table-title="附件管理">
      <template #toolbar-tools>
        <ElButton type="danger" :disabled="!vxeCheckboxChecked(gridApi)" @click="onBatchDelete">
          <Trash2 class="mr-1 size-4" />
          批量删除
        </ElButton>
      </template>

      <!-- 文件类型图标 -->
      <template #file_ext="{ row }">
        <span :class="getFileIconByExt(row.file_ext)" class="size-6 text-blue-500"></span>
      </template>
    </Grid>
  </Page>
</template>
