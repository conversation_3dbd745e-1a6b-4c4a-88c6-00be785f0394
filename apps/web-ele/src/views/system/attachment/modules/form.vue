<script lang="ts" setup>
import type { Attachment } from '../data';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { Copy, ExternalLink } from '@vben/icons';

import { ElButton, ElDivider, ElImage, ElMessage } from 'element-plus';

import { useVbenForm } from '#/adapter/form';

import { isImageFile, useViewSchema } from '../data';

const formData = ref<Attachment>();

const getTitle = computed(() => {
  return `附件详情 - ${formData.value?.filename || ''}`;
});

const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: useViewSchema(),
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<Attachment>();
      if (data) {
        formData.value = data;
        formApi.setValues(formData.value);
      }
    }
  },
});

/**
 * 复制链接
 */
function copyUrl() {
  if (formData.value?.url) {
    navigator.clipboard
      .writeText(formData.value.url)
      .then(() => {
        ElMessage.success('链接已复制到剪贴板');
      })
      .catch(() => {
        ElMessage.error('复制失败');
      });
  }
}

/**
 * 下载文件
 */
function downloadFile() {
  if (formData.value?.url) {
    const link = document.createElement('a');
    link.href = formData.value.url;
    link.download = formData.value.filename;
    link.target = '_blank';
    document.body.append(link);
    link.click();
    link.remove();
  }
}

/**
 * 在新窗口打开
 */
function openInNewWindow() {
  if (formData.value?.url) {
    window.open(formData.value.url, '_blank');
  }
}
</script>

<template>
  <Modal :title="getTitle" width="800px">
    <div class="space-y-6">
      <!-- 文件预览区域 -->
      <div class="rounded-lg bg-gray-50 p-6 text-center">
        <div class="mb-4">
          <!-- 图片预览 -->
          <ElImage
            v-if="formData && isImageFile(formData.mime_type)"
            :src="formData.url"
            class="max-h-64 max-w-full rounded shadow-md"
            :preview-src-list="[formData.url]"
            preview-teleported
            fit="contain"
          />
          <!-- 非图片文件图标 -->
          <div v-else class="flex flex-col items-center">
            <span :class="getFileTypeIcon(formData?.file_type_icon || 'file')" class="mb-2 size-16 text-blue-500"></span>
            <span class="text-gray-600">{{ formData?.filename }}</span>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex justify-center gap-3">
          <ElButton type="primary" @click="copyUrl">
            <Copy class="mr-1 size-4" />
            复制链接
          </ElButton>
          <ElButton type="success" @click="downloadFile">
            <Download class="mr-1 size-4" />
            下载文件
          </ElButton>
          <ElButton @click="openInNewWindow">
            <ExternalLink class="mr-1 size-4" />
            新窗口打开
          </ElButton>
        </div>
      </div>

      <ElDivider>文件信息</ElDivider>

      <!-- 表单信息 -->
      <Form class="mx-4" />
    </div>
  </Modal>
</template>

<style scoped>
:deep(.el-form-item__label) {
  font-weight: 600;
}

:deep(.el-input__inner) {
  background-color: #f5f5f5;
}

:deep(.el-textarea__inner) {
  background-color: #f5f5f5;
}
</style>
