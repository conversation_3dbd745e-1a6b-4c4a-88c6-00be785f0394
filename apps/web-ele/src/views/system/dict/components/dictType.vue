<script setup lang="ts">
import type { System } from '@vben/api';

import { computed, nextTick, ref } from 'vue';

import { createDictTypeApi, deleteDictTypeApi, getDictTypeListApi, updateDictTypeApi } from '@vben/api';
import { useVbenModal, VbenButton } from '@vben/common-ui';

import { Delete, Edit, Plus, Search } from '@element-plus/icons-vue';
import { ElButton, ElInput, ElMessage, ElScrollbar, ElSwitch } from 'element-plus';

import { useHandleData } from '#/composables/useHandleData';

// 定义组件的 emits
const emit = defineEmits<{
  dictSelect: [dict: null | System.DictType];
}>();

// 搜索关键字
const searchKeyword = ref('');

// 当前选中的字典代码
const selectedDictCode = ref<string>('');

// 字典类型数据
const dictTypes = ref<System.DictType[]>([]);
const loading = ref(false);

// 滚动条引用
const scrollbarRef = ref();

const fetchDictTypes = async () => {
  loading.value = true;
  try {
    const res = await getDictTypeListApi();
    dictTypes.value = res;
    // 数据加载完成后更新滚动条
    await nextTick();
    // 强制更新滚动条
    setTimeout(() => {
      if (scrollbarRef.value) {
        scrollbarRef.value.update();
      }
    }, 100);
  } catch {
    ElMessage.error('获取字典类型失败');
  } finally {
    loading.value = false;
  }
};

// 过滤后的字典列表
const filteredDictTypes = computed(() => {
  return searchKeyword.value
    ? dictTypes.value.filter((item) => item.name.includes(searchKeyword.value) || item.code.includes(searchKeyword.value))
    : dictTypes.value;
});

// 模态框相关
const [Modal, modalApi] = useVbenModal();

// 表单数据
const formData = ref<Partial<System.DictType>>({
  id: undefined,
  name: '',
  code: '',
  remark: '',
  status: 1,
});

// 编辑模式标识
const isEditMode = ref(false);
const editingId = ref<null | number>(null);

// 选择字典
const selectDict = (dict: System.DictType) => {
  selectedDictCode.value = dict.code;
  emit('dictSelect', dict);
};

// 编辑字典
const editDict = (dict: System.DictType, event: Event) => {
  event.stopPropagation();
  isEditMode.value = true;
  editingId.value = typeof dict.id === 'string' ? Number(dict.id) : dict.id;
  formData.value = {
    id: editingId.value,
    name: dict.name,
    code: dict.code,
    remark: dict.remark,
    status: dict.status,
  };
  modalApi.open();
};

// 删除字典
const deleteDict = async (dict: System.DictType, event: Event) => {
  event.stopPropagation();
  try {
    await useHandleData(deleteDictTypeApi, dict.id, `删除字典`);
    // 如果删除的是当前选中的字典，清空选中状态
    if (selectedDictCode.value === dict.code) {
      selectedDictCode.value = '';
      emit('dictSelect', null);
    }
    fetchDictTypes();
  } catch {
    ElMessage.error('删除失败');
  }
};

// 新增字典
const addDict = () => {
  isEditMode.value = false;
  editingId.value = null;
  formData.value = {
    id: undefined,
    name: '',
    code: '',
    remark: '',
    status: 1,
  };
  modalApi.open();
};

// 保存字典
const saveDict = async () => {
  if (!formData.value.name || !formData.value.code) {
    ElMessage.warning('请填写完整的字典信息');
    return;
  }
  try {
    if (isEditMode.value && editingId.value !== null) {
      await updateDictTypeApi({
        id: editingId.value,
        name: formData.value.name,
        code: formData.value.code,
        remark: formData.value.remark,
        status: formData.value.status,
      });
      ElMessage.success('更新成功');
    } else {
      await createDictTypeApi({
        name: formData.value.name,
        code: formData.value.code,
        remark: formData.value.remark,
        status: formData.value.status,
      });
      ElMessage.success('新增成功');
    }
    modalApi.close();
    fetchDictTypes();
  } catch {
    ElMessage.error('操作失败');
  }
};

// 取消操作
const cancelEdit = () => {
  modalApi.close();
};

// 初始化获取数据
fetchDictTypes();
</script>

<template>
  <div class="dict-type-container mr-2">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <ElButton type="primary" size="default" :icon="Plus" @click="addDict" class="add-button block"> 新增字典 </ElButton>
      <ElInput v-model="searchKeyword" placeholder="搜索" size="default" clearable :prefix-icon="Search" class="search-input" />
    </div>

    <!-- 字典列表区域 -->
    <div class="dict-list-container">
      <div class="h-full">
        <ElScrollbar ref="scrollbarRef" height="100%">
          <div class="dict-list" v-loading="loading">
            <div
              v-for="dict in filteredDictTypes"
              :key="dict.id"
              class="dict-item"
              :class="{ 'dict-item--selected': selectedDictCode === dict.code }"
              @click="selectDict(dict)"
            >
              <!-- 字典基本信息 -->
              <div class="dict-info">
                <div class="dict-header">
                  <h4 class="dict-name">{{ dict.name }}</h4>
                  <div class="dict-status">
                    <span class="status-dot" :class="{ 'status-dot--active': dict.status === 1 }"></span>
                    <span class="status-text">{{ dict.status === 1 ? '启用' : '禁用' }}</span>
                  </div>
                </div>
                <div class="dict-meta">
                  <span class="dict-code">{{ dict.code }}</span>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="dict-actions">
                <ElButton
                  :icon="Edit"
                  size="default"
                  type="primary"
                  link
                  @click="editDict(dict, $event)"
                  title="编辑"
                  class="action-button"
                />
                <ElButton
                  :icon="Delete"
                  size="default"
                  type="danger"
                  link
                  @click="deleteDict(dict, $event)"
                  title="删除"
                  class="action-button"
                />
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="filteredDictTypes.length === 0" class="empty-state">
              <div class="empty-icon">📝</div>
              <p class="empty-text">暂无匹配的字典类型</p>
            </div>
          </div>
        </ElScrollbar>
      </div>
    </div>

    <!-- 字典编辑/新增模态框 -->
    <Modal class="w-[400px]" :title="isEditMode ? '编辑字典' : '新增字典'">
      <div class="space-y-4 p-4">
        <!-- 字典名称 -->
        <div>
          <label class="mb-1 block text-xs font-medium text-gray-700 dark:text-gray-300">字典名称</label>
          <ElInput v-model="formData.name" placeholder="请输入字典名称" />
        </div>

        <!-- 字典标识 -->
        <div>
          <label class="mb-1 block text-xs font-medium text-gray-700 dark:text-gray-300">字典标识</label>
          <ElInput v-model="formData.code" placeholder="请输入字典标识" />
        </div>

        <!-- 状态 -->
        <div>
          <label class="mb-1 block text-xs font-medium text-gray-700 dark:text-gray-300">状态</label>
          <ElSwitch v-model="formData.status" :active-value="1" :inactive-value="2" />
        </div>

        <!-- 备注 -->
        <div>
          <label class="mb-1 block text-xs font-medium text-gray-700 dark:text-gray-300">备注</label>
          <ElInput v-model="formData.remark" placeholder="请输入备注" type="textarea" />
        </div>
      </div>

      <!-- 模态框底部按钮 -->
      <template #footer>
        <div class="flex justify-end space-x-2">
          <VbenButton variant="outline" @click="cancelEdit"> 取消 </VbenButton>
          <VbenButton type="primary" @click="saveDict">
            {{ isEditMode ? '更新' : '保存' }}
          </VbenButton>
        </div>
      </template>
    </Modal>
  </div>
</template>

<style scoped>
.dict-type-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  background: hsl(var(--card));
  border-radius: 8px;
}

/* 顶部工具栏 */
.toolbar {
  display: flex;
  flex-direction: row;
  gap: 12px;
  padding: 16px 16px 8px;
  background: hsl(var(--card));
}

.add-button {
  flex-shrink: 0;
  font-weight: 500;
  border-radius: 6px;
}

.search-input {
  width: 100%;
}

/* 字典列表容器 */
.dict-list-container {
  flex: 1;
  height: 100%;
  overflow: hidden;
}

.dict-list {
  padding: 0 16px 8px;
}

/* 字典项样式 */
.dict-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  margin-bottom: 8px;
  cursor: pointer;
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 8px;
}

.dict-item:hover {
  background: hsl(var(--accent));
  border-color: hsl(var(--primary) / 30%);
  box-shadow: 0 4px 12px hsl(var(--primary) / 10%);
}

.dict-item--selected {
  background: hsl(var(--accent));
  border-color: hsl(var(--primary));
  border-width: 1px;
  box-shadow: 0 0 0 3px hsl(var(--primary) / 10%);
}

/* 字典信息区域 */
.dict-info {
  flex: 1;
  min-width: 0;
}

.dict-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.dict-name {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: hsl(var(--foreground));
}

.dict-status {
  display: flex;
  gap: 4px;
  align-items: center;
  margin-left: auto;
}

.status-dot {
  width: 8px;
  height: 8px;
  background: hsl(var(--muted-foreground));
  border-radius: 50%;
}

.status-dot--active {
  background: hsl(var(--success, 142 71% 45%));
}

.status-text {
  font-size: 12px;
  color: hsl(var(--muted-foreground));
}

.dict-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

.dict-code {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  font-size: 12px;
  font-weight: 400;
  color: hsl(var(--muted-foreground) / 50%);
}

/* 操作按钮区域 */
.dict-actions {
  position: absolute;
  top: 50%;
  right: 12px;
  z-index: 10;
  display: flex;
  padding: 12px;
  background: hsl(var(--card));
  border-radius: 6px;
  box-shadow: 0 2px 8px hsl(var(--shadow) / 15%);
  opacity: 0;
  transform: translateY(-50%);
}

.dict-item:hover .dict-actions {
  opacity: 1;
}

.action-button {
  font-size: 16px;
  border-radius: 4px;
}

.action-button:hover {
  background: hsl(var(--accent));
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 16px;
  text-align: center;
}

.empty-icon {
  margin-bottom: 16px;
  font-size: 48px;
  opacity: 0.5;
}

.empty-text {
  margin: 0;
  font-size: 14px;
  color: hsl(var(--muted-foreground));
}
</style>
