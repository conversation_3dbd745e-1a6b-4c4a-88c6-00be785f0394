import type { System } from '@vben/api';

import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';

import { getDictTypeListApi } from '@vben/api';

// 字典数据表单 schema
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'ApiSelect',
      // 对应组件的参数
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: { id: number; name: string }[]) => {
          console.warn(data);
          return data.map((item: any) => ({
            label: item.name,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getDictTypeListApi,
      },
      disabled: true,
      // 字段名
      fieldName: 'type_id',
      // 界面显示的label
      label: '字典类型',
    },
    {
      component: 'Input',
      fieldName: 'code',
      label: '字典标识',
      disabled: true,
    },
    {
      component: 'Input',
      fieldName: 'label',
      label: '字典标签',
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'value',
      label: '字典键值',
      rules: 'required',
    },
    {
      component: 'InputNumber',
      fieldName: 'sort',
      label: '排序',
      defaultValue: 100,
    },
    {
      component: 'Switch',
      fieldName: 'status',
      label: '状态',
      defaultValue: 1,
      componentProps: {
        activeValue: 1,
        inactiveValue: 2,
      },
    },
    {
      component: 'Input',
      fieldName: 'remark',
      label: '备注',
      componentProps: {
        type: 'textarea',
      },
    },
  ];
}

// 搜索 schema
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'label',
      label: '字典标签',
    },
    {
      component: 'Input',
      fieldName: 'value',
      label: '字典值',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '状态',
      componentProps: {
        options: [
          { label: '正常', value: 1 },
          { label: '禁用', value: 2 },
        ],
      },
    },
  ];
}

export function useColumns<T = System.DictData>(
  onActionClick: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<any>,
  onSortChange?: (newSort: any, row: T) => PromiseLike<any>,
): VxeTableGridOptions['columns'] {
  return [
    { type: 'checkbox', width: 50, fixed: 'left' },
    {
      field: 'drag',
      title: 'Sort',
      width: 50,
      align: 'center',
      dragSort: true,
    },
    {
      field: 'id',
      title: 'ID',
      width: 80,
    },
    {
      field: 'label',
      title: '字典标签',
      minWidth: 120,
    },
    {
      field: 'value',
      title: '字典值',
      minWidth: 120,
    },
    {
      field: 'sort',
      title: '排序',
      minWidth: 180,
      cellRender: {
        name: 'CellInputNumber',
        attrs: { beforeChange: onSortChange },
        props: {
          min: 0,
          step: 1,
          precision: 0,
        },
      },
    },
    {
      field: 'created_at',
      title: '创建时间',
      cellRender: {
        name: 'CellDatetime',
      },
    },
    {
      cellRender: {
        attrs: { beforeChange: onStatusChange },
        name: onStatusChange ? 'CellSwitch' : 'CellTag',
      },
      field: 'status',
      title: '状态',
      width: 100,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'label',
          nameTitle: '字典',
          onClick: onActionClick,
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 150,
    },
  ];
}
