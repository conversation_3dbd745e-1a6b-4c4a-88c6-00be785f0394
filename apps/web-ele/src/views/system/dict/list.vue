<script lang="ts" setup>
import type { System } from '@vben/api';

import type { OnActionClickParams, VxeTableGridOptions } from '#/adapter/vxe-table';

import { ref } from 'vue';

import { batchSortDictDataApi, deleteDictData<PERSON>pi, getDictDataList<PERSON>pi, updateDictDataApi } from '@vben/api';
import { Page, useVbenModal } from '@vben/common-ui';
import { Plus } from '@vben/icons';

import { ElButton, ElEmpty, ElMessage, ElSplitter, ElSplitterPanel } from 'element-plus';

import { useVbenVxeGrid, vxeCheckboxChecked } from '#/adapter/vxe-table';
import { useHandleData } from '#/composables/useHandleData';

import DictType from './components/dictType.vue';
import { useColumns } from './data';
import Form from './modules/form.vue';

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
});

// 当前选中的字典类型 code
const selectedDict = ref<null | System.DictType>(null);

// 监听 DictType 选择
function onDictTypeSelect(dict: null | System.DictType) {
  selectedDict.value = dict;
  refreshGrid();
}

/**
 * 操作按钮点击事件
 * @param {OnActionClickParams<System.DictData>} param
 */
function onActionClick({ code, row }: OnActionClickParams<System.DictData>) {
  const actions: Record<string, () => void> = {
    edit: () => open(row),
    delete: () => onDelete(row),
  };
  actions[code]?.();
}

/**
 * 打开表单
 * @param row 字典数据
 */
function open(row: Partial<System.DictData> = {}) {
  formModalApi.setData({ ...row, type_id: selectedDict.value?.id, code: selectedDict.value?.code }).open();
}

/**
 * 删除字典数据
 * @param row
 */
async function onDelete(row: System.DictData) {
  await deleteDictDataApi(row.id);
  ElMessage.success('删除成功');
  refreshGrid();
}

/**
 * 切换状态
 * @param newStatus 新状态
 * @param row 字典数据
 */
function onStatusChange(newStatus: number, row: System.DictData) {
  return useHandleData(updateDictDataApi, { id: row.id, status: newStatus }, `切换状态`);
}

async function onSortChange(newSort: any, row: System.DictData) {
  await updateDictDataApi({ id: row.id, sort: newSort });
  ElMessage.success('排序成功');
  refreshGrid();
}

function onBatchDelete() {
  // TODO: 批量删除逻辑
  console.warn('onBatchDelete', gridApi.grid.getCheckboxRecords());
}

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}

const [Grid, gridApi] = useVbenVxeGrid({
  gridEvents: {
    rowDragend: async (_res: any) => {
      const { fullData } = gridApi.grid.getTableData();
      const ids = fullData.map((item: any) => Number(item.id));
      await batchSortDictDataApi(ids);
      ElMessage.success('排序成功');
      refreshGrid();
    },
  },
  gridOptions: {
    columns: useColumns(onActionClick, onStatusChange, onSortChange),
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          // 通过字典类型 code 查询字典数据
          if (!selectedDict.value) return { items: [] };
          return await getDictDataListApi({
            code: selectedDict.value.code,
            page: page.currentPage,
            limit: page.pageSize,
          });
        },
      },
    },
    showOverflow: false,
    rowConfig: {
      drag: true,
    },
  } as VxeTableGridOptions,
});
</script>
<template>
  <Page auto-content-height>
    <ElSplitter>
      <ElSplitterPanel size="360px" min="280px" max="500px" collapsible>
        <DictType @dict-select="onDictTypeSelect" />
      </ElSplitterPanel>
      <ElSplitterPanel :min="200" collapsible>
        <div class="ml-2 h-full overflow-hidden">
          <FormModal @success="refreshGrid" />
          <Grid :table-title="`${selectedDict?.name || ''}字典数据列表`">
            <template #toolbar-tools>
              <ElButton type="primary" :disabled="!selectedDict" @click="() => open()">
                <Plus class="size-5" />
                新增字典数据
              </ElButton>
              <ElButton type="danger" :disabled="!vxeCheckboxChecked(gridApi)" @click="onBatchDelete">
                <span class="icon-[mdi--trash-can-outline] size-5"></span>
                批量删除
              </ElButton>
            </template>
            <template #empty>
              <ElEmpty description="请从左侧选择字典" />
            </template>
          </Grid>
        </div>
      </ElSplitterPanel>
    </ElSplitter>
  </Page>
</template>
