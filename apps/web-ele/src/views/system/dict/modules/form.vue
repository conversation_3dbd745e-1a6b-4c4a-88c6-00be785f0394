<script lang="ts" setup>
import type { System } from '@vben/api';

import { computed, ref } from 'vue';

import { createDictDataApi, updateDictDataApi } from '@vben/api';
import { useVbenModal } from '@vben/common-ui';

import { ElButton } from 'element-plus';

import { useVbenForm } from '#/adapter/form';

import { useFormSchema } from '../data';

const emits = defineEmits<{
  success: [];
}>();

const userId = ref<number>();
const formData = ref<System.DictData>();

const [Form, formApi] = useVbenForm({
  schema: useFormSchema(),
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    await handleFormSubmit();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      handleModalOpen();
    }
  },
});

// 计算属性
const isViewMode = computed(() => !!modalApi.useStore().value.isView);

const modalTitle = computed(() => {
  if (isViewMode.value) return '查看字典数据';
  return userId.value ? '编辑字典数据' : '新增字典数据';
});

// 业务逻辑函数
async function handleFormSubmit(): Promise<void> {
  const { valid } = await formApi.validate();
  if (!valid) return;

  const values = await formApi.getValues();
  modalApi.lock();

  try {
    const apiCall = userId.value ? updateDictDataApi({ id: userId.value, ...values }) : createDictDataApi(values);

    await apiCall;
    emits('success');
    modalApi.close();
  } catch (error) {
    modalApi.unlock();
    // 这里可以添加具体的错误处理逻辑
    console.error('保存字典数据失败:', error);
  }
}

function handleModalOpen(): void {
  // 设置查看模式的表单状态
  if (isViewMode.value) {
    formApi.setState({ commonConfig: { disabled: true } });
  }

  const dictData = modalApi.getData<System.DictData>();
  formApi.resetForm();

  if (dictData) {
    initializeFormWithUserData(dictData);
  } else {
    resetFormToDefault();
  }
}

function initializeFormWithUserData(dictData: System.DictData): void {
  formData.value = dictData;
  userId.value = dictData.id;

  // 处理并设置表单数据
  const processedData = { ...dictData };
  console.warn(processedData);

  formApi.setValues(processedData);
}

function resetFormToDefault(): void {
  userId.value = undefined;
  formData.value = undefined;
}
</script>

<template>
  <Modal :title="modalTitle">
    <Form />
    <template #footer v-if="!isViewMode">
      <ElButton type="primary" @click="modalApi.onConfirm()"> 保存 </ElButton>
      <ElButton @click="modalApi.close()"> 取消 </ElButton>
    </template>
  </Modal>
</template>

<style lang="css" scoped></style>
