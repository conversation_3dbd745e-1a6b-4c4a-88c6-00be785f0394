import type { System } from '@vben/api';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { OnActionClickFn } from '#/adapter/vxe-table';

/**
 * 获取表格列配置
 * @param onActionClick 表格操作按钮点击事件
 */
export function useColumns<T = System.LogLogin>(onActionClick?: OnActionClickFn<T>): VxeTableGridOptions['columns'] {
  return [
    { type: 'checkbox', width: 60 },
    { align: 'center', field: 'id', title: 'ID', width: 80 },
    { field: 'username', title: '用户名', width: 120, sortable: true },
    { field: 'login_ip', title: '登录IP', width: 140 },
    { field: 'location', title: '登录地点', width: 120 },
    { field: 'browser', title: '浏览器', width: 150 },
    { field: 'os', title: '操作系统', width: 120 },
    { field: 'device_type', title: '设备类型', width: 100 },
    {
      cellRender: {
        name: 'CellTag',
        options: [
          { label: '成功', type: 'primary', value: 1 },
          { label: '失败', type: 'danger', value: 2 },
        ],
      },
      field: 'status',
      title: '状态',
      width: 80,
    },
    {
      field: 'failure_reason',
      title: '失败原因',
      showOverflow: 'tooltip',
    },
    {
      field: 'login_time',
      title: '登录时间',
      width: 180,
      formatter: ({ cellValue }) => {
        return cellValue ? new Date(cellValue * 1000).toLocaleString() : '';
      },
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameTitle: '日志',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: ['delete'],
      },
      field: 'operation',
      fixed: 'right',
      headerAlign: 'center',
      showOverflow: false,
      title: '操作',
      width: 120,
    },
  ];
}
