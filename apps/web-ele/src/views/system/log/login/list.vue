<script lang="ts" setup>
import type { System } from '@vben/api';

import type { OnActionClickParams, VxeTableGridOptions } from '#/adapter/vxe-table';

import { batchDeleteLogLoginApi, deleteLogLoginApi, getLog<PERSON>oginListApi } from '@vben/api';
import { Page } from '@vben/common-ui';
import { Trash2 } from '@vben/icons';

import { ElButton, ElMessage } from 'element-plus';

import { useVbenVxeGrid, vxeCheckboxChecked } from '#/adapter/vxe-table';
import { useHandleData } from '#/composables/useHandleData';

import { useColumns } from './data';

/**
 * 操作按钮点击事件
 * @param {OnActionClickParams<System.LogLogin>} param
 */
function onActionClick({ code, row }: OnActionClickParams<System.LogLogin>) {
  const actions: Record<string, () => void> = {
    delete: () => onDelete(row),
  };

  actions[code]?.();
}

/**
 * 删除登录日志
 * @param row
 */
async function onDelete(row: System.LogLogin) {
  await deleteLogLoginApi(row.id);
  ElMessage.success('删除成功');
  refreshGrid();
}

/**
 * 批量删除
 */
async function onBatchDelete() {
  const ids = gridApi.grid.getCheckboxRecords().map((record: System.LogLogin) => record.id);
  await useHandleData(batchDeleteLogLoginApi, ids, '批量删除登录日志');
  refreshGrid();
}

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    collapsed: true,
    schema: [
      {
        label: '用户名',
        fieldName: 'username',
        component: 'Input',
      },
      {
        label: 'IP',
        fieldName: 'ip',
        component: 'Input',
      },
    ],
    submitOnChange: true,
    wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  },
  gridOptions: {
    columns: useColumns(onActionClick),
    proxyConfig: {
      sort: true,
      ajax: {
        query: async ({ page, sort }, formValues) => {
          const result = await getLogLoginListApi({
            page: page.currentPage,
            limit: page.pageSize,
            sortField: sort.field,
            sortOrder: sort.order,
            ...formValues,
          });
          return result;
        },
      },
    },
    showOverflow: false,
  } as VxeTableGridOptions,
});
</script>

<template>
  <Page auto-content-height>
    <Grid table-title="登录日志">
      <template #toolbar-tools>
        <ElButton type="danger" :disabled="!vxeCheckboxChecked(gridApi)" @click="onBatchDelete">
          <Trash2 class="mr-1 size-4" />
          批量删除
        </ElButton>
      </template>
    </Grid>
  </Page>
</template>
