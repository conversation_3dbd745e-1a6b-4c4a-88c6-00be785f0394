import type { System } from '@vben/api';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { OnActionClickFn } from '#/adapter/vxe-table';

/**
 * 获取表格列配置
 * @param onActionClick 表格操作按钮点击事件
 */
export function useColumns<T = System.LogOperate>(onActionClick?: OnActionClickFn<T>): VxeTableGridOptions['columns'] {
  return [
    { type: 'checkbox', width: 60 },
    {
      align: 'center',
      field: 'id',
      title: 'ID',
      width: 80,
    },
    {
      field: 'module',
      title: '模块',
      width: 100,
    },
    {
      field: 'operate',
      title: '操作',
    },
    {
      field: 'method',
      title: '请求方式',
      width: 100,
      cellRender: {
        name: 'CellTag',
        options: [
          { label: 'GET', color: 'success', value: 'GET' },
          { label: 'POST', color: 'primary', value: 'POST' },
          { label: 'PUT', color: 'warning', value: 'PUT' },
          { label: 'DELETE', color: 'danger', value: 'DELETE' },
        ],
      },
    },
    {
      field: 'route',
      title: '路由',
      width: 200,
      showOverflow: 'tooltip',
    },
    {
      field: 'ip',
      title: 'IP地址',
      width: 140,
    },
    {
      cellRender: {
        name: 'CellStatus',
        options: [
          { label: '成功', color: 'success', value: 1 },
          { label: '失败', color: 'error', value: 2 },
        ],
      },
      field: 'status',
      title: '状态',
      width: 80,
    },
    {
      field: 'http_code',
      title: 'HTTP状态码',
      width: 120,
    },
    {
      field: 'time_taken',
      title: '执行时间(ms)',
      width: 120,
    },
    {
      field: 'error_message',
      title: '错误信息',
      width: 200,
      showOverflow: 'tooltip',
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'module',
          nameTitle: '操作日志',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: ['view', 'delete'],
      },
      field: 'operation',
      fixed: 'right',
      headerAlign: 'center',
      showOverflow: false,
      title: '操作',
      width: 150,
    },
  ];
}
