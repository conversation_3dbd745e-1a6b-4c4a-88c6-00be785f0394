<script lang="ts" setup>
import type { System } from '@vben/api';

import type { OnActionClickParams, VxeTableGridOptions } from '#/adapter/vxe-table';

import { batchDeleteLogOperateApi, deleteLogOperateApi, getLogOperateListApi } from '@vben/api';
import { Page, useVbenModal } from '@vben/common-ui';

import { ElButton, ElMessage, ElMessageBox } from 'element-plus';

import { useVbenVxeGrid, vxeCheckboxChecked } from '#/adapter/vxe-table';

import { useColumns } from './data';
import DetailModal from './modules/detail.vue';

const [DetailModalComponent, detailModalApi] = useVbenModal({
  connectedComponent: DetailModal,
});

/**
 * 操作按钮点击事件
 * @param {OnActionClickParams<System.LogOperate>} param
 */
function onActionClick({ code, row }: OnActionClickParams<System.LogOperate>) {
  const actions: Record<string, () => void> = {
    view: () => onView(row),
    delete: () => onDelete(row),
  };

  actions[code]?.();
}

/**
 * 查看详情
 * @param row
 */
function onView(row: System.LogOperate) {
  detailModalApi.setData(row).open();
}

/**
 * 删除操作日志
 * @param row
 */
async function onDelete(row: System.LogOperate) {
  try {
    await ElMessageBox.confirm(`确定要删除"${row.module}"的操作日志吗？`, '删除确认', {
      type: 'warning',
    });

    await deleteLogOperateApi(row.id);
    ElMessage.success('删除成功');
    refreshGrid();
  } catch {
    // 用户取消删除
  }
}

/**
 * 批量删除
 */
async function onBatchDelete() {
  const records = gridApi.grid.getCheckboxRecords();
  if (records.length === 0) {
    ElMessage.warning('请选择要删除的记录');
    return;
  }

  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${records.length} 条操作日志吗？`, '批量删除确认', {
      type: 'warning',
    });

    const ids = records.map((record: System.LogOperate) => record.id);
    await batchDeleteLogOperateApi(ids);
    ElMessage.success('批量删除成功');
    refreshGrid();
  } catch {
    // 用户取消删除
  }
}

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: useColumns(onActionClick),
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const params = {
            page: page.currentPage,
            limit: page.pageSize,
            ...formValues,
          };
          const result = await getLogOperateListApi(params);
          return result;
        },
      },
    },
    showOverflow: false,
  } as VxeTableGridOptions,
});
</script>

<template>
  <Page auto-content-height>
    <DetailModalComponent />
    <Grid table-title="操作日志">
      <template #toolbar-tools>
        <ElButton type="danger" :disabled="!vxeCheckboxChecked(gridApi)" @click="onBatchDelete">
          <span class="icon-[mdi--delete-circle] mr-2 size-4"></span>
          批量删除
        </ElButton>
      </template>
    </Grid>
  </Page>
</template>
