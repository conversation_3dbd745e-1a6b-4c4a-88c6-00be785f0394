<script lang="ts" setup>
import type { System } from '@vben/api';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ElDescriptions, ElDescriptionsItem, ElTag } from 'element-plus';

const logData = ref<System.LogOperate>();

const [Modal, modalApi] = useVbenModal({
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<System.LogOperate>();
      if (data) {
        logData.value = data;
      }
    }
  },
});

const getStatusTag = computed(() => {
  if (!logData.value) return { type: 'info', text: '未知' };
  return logData.value.status === 1 ? { type: 'success', text: '成功' } : { type: 'danger', text: '失败' };
});

const getMethodTag = computed(() => {
  if (!logData.value) return { type: 'info', text: '未知' };
  const method = logData.value.method;
  const tagMap: Record<string, string> = {
    GET: 'success',
    POST: 'primary',
    PUT: 'warning',
    DELETE: 'danger',
  };
  return { type: tagMap[method] || 'info', text: method };
});

const formatTime = (timestamp: number) => {
  return timestamp ? new Date(timestamp * 1000).toLocaleString() : '';
};

const formatParams = (params: string) => {
  if (!params) return '';
  try {
    return JSON.stringify(JSON.parse(params), null, 2);
  } catch {
    return params;
  }
};
</script>

<template>
  <Modal title="操作日志详情" width="800px">
    <div v-if="logData" class="p-4">
      <ElDescriptions :column="2" border>
        <ElDescriptionsItem label="ID">
          {{ logData.id }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="模块">
          {{ logData.module }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="操作">
          {{ logData.operate }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="描述">
          {{ logData.description }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="请求方式">
          <ElTag :type="getMethodTag.type">{{ getMethodTag.text }}</ElTag>
        </ElDescriptionsItem>
        <ElDescriptionsItem label="路由">
          {{ logData.route }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="IP地址">
          {{ logData.ip }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="状态">
          <ElTag :type="getStatusTag.type">{{ getStatusTag.text }}</ElTag>
        </ElDescriptionsItem>
        <ElDescriptionsItem label="HTTP状态码">
          {{ logData.http_code }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="执行时间"> {{ logData.time_taken }}ms </ElDescriptionsItem>
        <ElDescriptionsItem label="开始时间">
          {{ formatTime(logData.start_at) }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="创建时间">
          {{ formatTime(logData.created_at) }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="请求参数" :span="2">
          <pre class="max-h-40 overflow-auto rounded bg-gray-50 p-2 text-sm">{{ formatParams(logData.params) }}</pre>
        </ElDescriptionsItem>
        <ElDescriptionsItem v-if="logData.error_message" label="错误信息" :span="2">
          <div class="rounded bg-red-50 p-2 text-sm text-red-500">
            {{ logData.error_message }}
          </div>
        </ElDescriptionsItem>
      </ElDescriptions>
    </div>
  </Modal>
</template>

<style scoped>
pre {
  word-wrap: break-word;
  white-space: pre-wrap;
}
</style>
