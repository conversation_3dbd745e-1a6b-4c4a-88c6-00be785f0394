@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  *,
  ::after,
  ::before {
    @apply border-border;

    box-sizing: border-box;
    border-style: solid;
    border-width: 0;
  }

  html {
    @apply text-foreground bg-background font-sans text-[100%];

    font-variation-settings: normal;
    line-height: 1.15;
    text-size-adjust: 100%;
    font-synthesis-weight: none;
    scroll-behavior: smooth;
    text-rendering: optimizelegibility;
    -webkit-tap-highlight-color: transparent;

    /* -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale; */
  }

  #app,
  body,
  html {
    @apply size-full;

    /* scrollbar-gutter: stable; */
  }

  body {
    min-height: 100vh;

    /* pointer-events: auto !important; */

    /* overflow: overlay; */

    /* -webkit-font-smoothing: antialiased; */

    /* -moz-osx-font-smoothing: grayscale; */
  }

  a,
  a:active,
  a:hover,
  a:link,
  a:visited {
    @apply no-underline;
  }

  ::view-transition-new(root),
  ::view-transition-old(root) {
    @apply animate-none mix-blend-normal;
  }

  ::view-transition-old(root) {
    @apply z-[1];
  }

  ::view-transition-new(root) {
    @apply z-[2147483646];
  }

  html.dark::view-transition-old(root) {
    @apply z-[2147483646];
  }

  html.dark::view-transition-new(root) {
    @apply z-[1];
  }

  input::placeholder,
  textarea::placeholder {
    @apply opacity-100;
  }

  /* input:-webkit-autofill {
    @apply border-none;

    box-shadow: 0 0 0 1000px transparent inset;
  } */

  input[type='number']::-webkit-inner-spin-button,
  input[type='number']::-webkit-outer-spin-button {
    @apply m-0 appearance-none;
  }

  /* 只有非mac下才进行调整，mac下使用默认滚动条 */
  html:not([data-platform='macOs']) {
    ::-webkit-scrollbar {
      @apply h-[10px] w-[10px];
    }

    ::-webkit-scrollbar-thumb {
      @apply bg-border rounded-sm border-none;
    }

    ::-webkit-scrollbar-track {
      @apply rounded-sm border-none bg-transparent shadow-none;
    }

    ::-webkit-scrollbar-button {
      @apply hidden;
    }
  }
}

@layer components {
  .flex-center {
    @apply flex items-center justify-center;
  }

  .flex-col-center {
    @apply flex flex-col items-center justify-center;
  }

  .outline-box {
    @apply outline-border relative cursor-pointer rounded-md p-1 outline outline-1;
  }

  .outline-box::after {
    @apply absolute left-1/2 top-1/2 z-20 h-0 w-[1px] rounded-sm opacity-0 outline outline-2 outline-transparent transition-all duration-300 content-[""];
  }

  .outline-box.outline-box-active {
    @apply outline-primary outline outline-2;
  }

  .outline-box.outline-box-active::after {
    display: none;
  }

  .outline-box:not(.outline-box-active):hover::after {
    @apply outline-primary left-0 top-0 h-full w-full p-1 opacity-100;
  }

  .vben-link {
    @apply text-primary hover:text-primary-hover active:text-primary-active cursor-pointer;
  }

  .card-box {
    @apply bg-card text-card-foreground border-border rounded-xl border;
  }
}

html.invert-mode {
  @apply invert;
}

html.grayscale-mode {
  @apply grayscale;
}
