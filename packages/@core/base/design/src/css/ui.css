.side-content {
  animation-duration: 0.2s;
  animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
}

.side-content[data-side='top'] {
  animation-name: slide-up;
}

.side-content[data-side='bottom'] {
  animation-name: slide-down;
}

.side-content[data-side='left'] {
  animation-name: slide-left;
}

.side-content[data-side='right'] {
  animation-name: slide-right;
}

.breadcrumb-transition-enter-active {
  transition:
    transform 0.4s cubic-bezier(0.76, 0, 0.24, 1),
    opacity 0.4s cubic-bezier(0.76, 0, 0.24, 1);
}

.breadcrumb-transition-leave-active {
  display: none;
}

.breadcrumb-transition-enter-from {
  opacity: 0;
  transform: translateX(30px) skewX(-30deg);
}

@keyframes slide-down {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-left {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-right {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.z-popup {
  z-index: var(--popup-z-index);
}
